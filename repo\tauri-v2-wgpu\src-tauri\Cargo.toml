[package]
name = "beta-desktop"
version = "0.0.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.4.1", features = [] }

[dependencies]
tauri = { version = "2.8.5", features = ["macos-private-api"] }
serde_json = "1.0"
pretty_env_logger = "0.5.0"
wgpu = "26.0.1"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[patch.crates-io]
#tauri = {path = "A:/tauri-apps/tauri-v2/core/tauri"}
# tauri = { git = "https://github.com/tauri-apps/tauri", branch = "feat/window-displayhandle" }

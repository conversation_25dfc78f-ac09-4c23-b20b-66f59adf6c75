[package]
name = "libmpv2-sys"
version = "4.0.0"
edition = "2021"
authors = ["kohsine <<EMAIL>>"]
license = "LGPL-2.1"
build = "build.rs"
description = "Libmpv bindings generated by bindgen"
repository = "https://github.com/kohsine/libmpv-rs"
keywords = ["media", "playback", "mpv", "libmpv"]

[build-dependencies.bindgen]
version = "0.69.4"
optional = true

# Workaround for https://github.com/rust-lang/rust-bindgen/issues/1313
[lib]
doctest = false
doc = false

[features]
# You can either use the pregenerated bindings, or gen new ones with bindgen
use-bindgen = ["bindgen"]

{"name": "mpv-tauri", "private": true, "scripts": {"build:plugin:mpv": "pnpm --filter ./plugins/tauri-plugin-mpv build", "build:plugin:libmpv": "pnpm --filter ./plugins/tauri-plugin-libmpv build", "build:example:mpv": "pnpm build:plugin:mpv && pnpm --filter tauri-plugin-mpv-example tauri build", "build:example:libmpv": "pnpm build:plugin:libmpv && pnpm --filter tauri-plugin-libmpv-example tauri build", "dev:example:mpv": "pnpm build:plugin:mpv && cd examples/mpv-example && pnpm tauri dev", "dev:example:libmpv": "pnpm build:plugin:libmpv && cd examples/libmpv-example && pnpm tauri dev", "lint": "eslint ."}, "devDependencies": {"@eslint/js": "^9.36.0", "eslint": "^9.36.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.4.0", "jiti": "^2.6.0", "typescript-eslint": "^8.44.1"}}
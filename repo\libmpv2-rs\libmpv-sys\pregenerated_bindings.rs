/* automatically generated by rust-bindgen 0.69.4 */

pub const _STDINT_H: u32 = 1;
pub const _FEATURES_H: u32 = 1;
pub const _DEFAULT_SOURCE: u32 = 1;
pub const __GLIBC_USE_ISOC2X: u32 = 0;
pub const __USE_ISOC11: u32 = 1;
pub const __USE_ISOC99: u32 = 1;
pub const __USE_ISOC95: u32 = 1;
pub const __USE_POSIX_IMPLICITLY: u32 = 1;
pub const _POSIX_SOURCE: u32 = 1;
pub const _POSIX_C_SOURCE: u32 = 200809;
pub const __USE_POSIX: u32 = 1;
pub const __USE_POSIX2: u32 = 1;
pub const __USE_POSIX199309: u32 = 1;
pub const __USE_POSIX199506: u32 = 1;
pub const __USE_XOPEN2K: u32 = 1;
pub const __USE_XOPEN2K8: u32 = 1;
pub const _ATFILE_SOURCE: u32 = 1;
pub const __WORDSIZE: u32 = 64;
pub const __WORDSIZE_TIME64_COMPAT32: u32 = 1;
pub const __SYSCALL_WORDSIZE: u32 = 64;
pub const __TIMESIZE: u32 = 64;
pub const __USE_MISC: u32 = 1;
pub const __USE_ATFILE: u32 = 1;
pub const __USE_FORTIFY_LEVEL: u32 = 0;
pub const __GLIBC_USE_DEPRECATED_GETS: u32 = 0;
pub const __GLIBC_USE_DEPRECATED_SCANF: u32 = 0;
pub const __GLIBC_USE_C2X_STRTOL: u32 = 0;
pub const _STDC_PREDEF_H: u32 = 1;
pub const __STDC_IEC_559__: u32 = 1;
pub const __STDC_IEC_60559_BFP__: u32 = 201404;
pub const __STDC_IEC_559_COMPLEX__: u32 = 1;
pub const __STDC_IEC_60559_COMPLEX__: u32 = 201404;
pub const __STDC_ISO_10646__: u32 = 201706;
pub const __GNU_LIBRARY__: u32 = 6;
pub const __GLIBC__: u32 = 2;
pub const __GLIBC_MINOR__: u32 = 39;
pub const _SYS_CDEFS_H: u32 = 1;
pub const __glibc_c99_flexarr_available: u32 = 1;
pub const __LDOUBLE_REDIRECTS_TO_FLOAT128_ABI: u32 = 0;
pub const __HAVE_GENERIC_SELECTION: u32 = 1;
pub const __GLIBC_USE_LIB_EXT2: u32 = 0;
pub const __GLIBC_USE_IEC_60559_BFP_EXT: u32 = 0;
pub const __GLIBC_USE_IEC_60559_BFP_EXT_C2X: u32 = 0;
pub const __GLIBC_USE_IEC_60559_EXT: u32 = 0;
pub const __GLIBC_USE_IEC_60559_FUNCS_EXT: u32 = 0;
pub const __GLIBC_USE_IEC_60559_FUNCS_EXT_C2X: u32 = 0;
pub const __GLIBC_USE_IEC_60559_TYPES_EXT: u32 = 0;
pub const _BITS_TYPES_H: u32 = 1;
pub const _BITS_TYPESIZES_H: u32 = 1;
pub const __OFF_T_MATCHES_OFF64_T: u32 = 1;
pub const __INO_T_MATCHES_INO64_T: u32 = 1;
pub const __RLIM_T_MATCHES_RLIM64_T: u32 = 1;
pub const __STATFS_MATCHES_STATFS64: u32 = 1;
pub const __KERNEL_OLD_TIMEVAL_MATCHES_TIMEVAL64: u32 = 1;
pub const __FD_SETSIZE: u32 = 1024;
pub const _BITS_TIME64_H: u32 = 1;
pub const _BITS_WCHAR_H: u32 = 1;
pub const _BITS_STDINT_INTN_H: u32 = 1;
pub const _BITS_STDINT_UINTN_H: u32 = 1;
pub const _BITS_STDINT_LEAST_H: u32 = 1;
pub const INT8_MIN: i32 = -128;
pub const INT16_MIN: i32 = -32768;
pub const INT32_MIN: i32 = -2147483648;
pub const INT8_MAX: u32 = 127;
pub const INT16_MAX: u32 = 32767;
pub const INT32_MAX: u32 = 2147483647;
pub const UINT8_MAX: u32 = 255;
pub const UINT16_MAX: u32 = 65535;
pub const UINT32_MAX: u32 = 4294967295;
pub const INT_LEAST8_MIN: i32 = -128;
pub const INT_LEAST16_MIN: i32 = -32768;
pub const INT_LEAST32_MIN: i32 = -2147483648;
pub const INT_LEAST8_MAX: u32 = 127;
pub const INT_LEAST16_MAX: u32 = 32767;
pub const INT_LEAST32_MAX: u32 = 2147483647;
pub const UINT_LEAST8_MAX: u32 = 255;
pub const UINT_LEAST16_MAX: u32 = 65535;
pub const UINT_LEAST32_MAX: u32 = 4294967295;
pub const INT_FAST8_MIN: i32 = -128;
pub const INT_FAST16_MIN: i64 = -9223372036854775808;
pub const INT_FAST32_MIN: i64 = -9223372036854775808;
pub const INT_FAST8_MAX: u32 = 127;
pub const INT_FAST16_MAX: u64 = 9223372036854775807;
pub const INT_FAST32_MAX: u64 = 9223372036854775807;
pub const UINT_FAST8_MAX: u32 = 255;
pub const UINT_FAST16_MAX: i32 = -1;
pub const UINT_FAST32_MAX: i32 = -1;
pub const INTPTR_MIN: i64 = -9223372036854775808;
pub const INTPTR_MAX: u64 = 9223372036854775807;
pub const UINTPTR_MAX: i32 = -1;
pub const PTRDIFF_MIN: i64 = -9223372036854775808;
pub const PTRDIFF_MAX: u64 = 9223372036854775807;
pub const SIG_ATOMIC_MIN: i32 = -2147483648;
pub const SIG_ATOMIC_MAX: u32 = 2147483647;
pub const SIZE_MAX: i32 = -1;
pub const WINT_MIN: u32 = 0;
pub const WINT_MAX: u32 = 4294967295;
pub const MPV_ENABLE_DEPRECATED: u32 = 1;
pub const MPV_RENDER_API_TYPE_OPENGL: &[u8; 7] = b"opengl\0";
pub const MPV_RENDER_API_TYPE_SW: &[u8; 3] = b"sw\0";
pub type wchar_t = ::std::os::raw::c_int;
#[repr(C)]
#[repr(align(16))]
#[derive(Debug, Copy, Clone)]
pub struct max_align_t {
    pub __clang_max_align_nonce1: ::std::os::raw::c_longlong,
    pub __bindgen_padding_0: u64,
    pub __clang_max_align_nonce2: u128,
}
#[test]
fn bindgen_test_layout_max_align_t() {
    const UNINIT: ::std::mem::MaybeUninit<max_align_t> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<max_align_t>(),
        32usize,
        concat!("Size of: ", stringify!(max_align_t)),
    );
    assert_eq!(
        ::std::mem::align_of::<max_align_t>(),
        16usize,
        concat!("Alignment of ", stringify!(max_align_t)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).__clang_max_align_nonce1) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(max_align_t),
            "::",
            stringify!(__clang_max_align_nonce1),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).__clang_max_align_nonce2) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(max_align_t),
            "::",
            stringify!(__clang_max_align_nonce2),
        ),
    );
}
pub type __u_char = ::std::os::raw::c_uchar;
pub type __u_short = ::std::os::raw::c_ushort;
pub type __u_int = ::std::os::raw::c_uint;
pub type __u_long = ::std::os::raw::c_ulong;
pub type __int8_t = ::std::os::raw::c_schar;
pub type __uint8_t = ::std::os::raw::c_uchar;
pub type __int16_t = ::std::os::raw::c_short;
pub type __uint16_t = ::std::os::raw::c_ushort;
pub type __int32_t = ::std::os::raw::c_int;
pub type __uint32_t = ::std::os::raw::c_uint;
pub type __int64_t = ::std::os::raw::c_long;
pub type __uint64_t = ::std::os::raw::c_ulong;
pub type __int_least8_t = __int8_t;
pub type __uint_least8_t = __uint8_t;
pub type __int_least16_t = __int16_t;
pub type __uint_least16_t = __uint16_t;
pub type __int_least32_t = __int32_t;
pub type __uint_least32_t = __uint32_t;
pub type __int_least64_t = __int64_t;
pub type __uint_least64_t = __uint64_t;
pub type __quad_t = ::std::os::raw::c_long;
pub type __u_quad_t = ::std::os::raw::c_ulong;
pub type __intmax_t = ::std::os::raw::c_long;
pub type __uintmax_t = ::std::os::raw::c_ulong;
pub type __dev_t = ::std::os::raw::c_ulong;
pub type __uid_t = ::std::os::raw::c_uint;
pub type __gid_t = ::std::os::raw::c_uint;
pub type __ino_t = ::std::os::raw::c_ulong;
pub type __ino64_t = ::std::os::raw::c_ulong;
pub type __mode_t = ::std::os::raw::c_uint;
pub type __nlink_t = ::std::os::raw::c_ulong;
pub type __off_t = ::std::os::raw::c_long;
pub type __off64_t = ::std::os::raw::c_long;
pub type __pid_t = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __fsid_t {
    pub __val: [::std::os::raw::c_int; 2usize],
}
#[test]
fn bindgen_test_layout___fsid_t() {
    const UNINIT: ::std::mem::MaybeUninit<__fsid_t> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<__fsid_t>(),
        8usize,
        concat!("Size of: ", stringify!(__fsid_t)),
    );
    assert_eq!(
        ::std::mem::align_of::<__fsid_t>(),
        4usize,
        concat!("Alignment of ", stringify!(__fsid_t)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).__val) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(__fsid_t),
            "::",
            stringify!(__val)
        ),
    );
}
pub type __clock_t = ::std::os::raw::c_long;
pub type __rlim_t = ::std::os::raw::c_ulong;
pub type __rlim64_t = ::std::os::raw::c_ulong;
pub type __id_t = ::std::os::raw::c_uint;
pub type __time_t = ::std::os::raw::c_long;
pub type __useconds_t = ::std::os::raw::c_uint;
pub type __suseconds_t = ::std::os::raw::c_long;
pub type __suseconds64_t = ::std::os::raw::c_long;
pub type __daddr_t = ::std::os::raw::c_int;
pub type __key_t = ::std::os::raw::c_int;
pub type __clockid_t = ::std::os::raw::c_int;
pub type __timer_t = *mut ::std::os::raw::c_void;
pub type __blksize_t = ::std::os::raw::c_long;
pub type __blkcnt_t = ::std::os::raw::c_long;
pub type __blkcnt64_t = ::std::os::raw::c_long;
pub type __fsblkcnt_t = ::std::os::raw::c_ulong;
pub type __fsblkcnt64_t = ::std::os::raw::c_ulong;
pub type __fsfilcnt_t = ::std::os::raw::c_ulong;
pub type __fsfilcnt64_t = ::std::os::raw::c_ulong;
pub type __fsword_t = ::std::os::raw::c_long;
pub type __ssize_t = ::std::os::raw::c_long;
pub type __syscall_slong_t = ::std::os::raw::c_long;
pub type __syscall_ulong_t = ::std::os::raw::c_ulong;
pub type __loff_t = __off64_t;
pub type __caddr_t = *mut ::std::os::raw::c_char;
pub type __intptr_t = ::std::os::raw::c_long;
pub type __socklen_t = ::std::os::raw::c_uint;
pub type __sig_atomic_t = ::std::os::raw::c_int;
pub type int_least8_t = __int_least8_t;
pub type int_least16_t = __int_least16_t;
pub type int_least32_t = __int_least32_t;
pub type int_least64_t = __int_least64_t;
pub type uint_least8_t = __uint_least8_t;
pub type uint_least16_t = __uint_least16_t;
pub type uint_least32_t = __uint_least32_t;
pub type uint_least64_t = __uint_least64_t;
pub type int_fast8_t = ::std::os::raw::c_schar;
pub type int_fast16_t = ::std::os::raw::c_long;
pub type int_fast32_t = ::std::os::raw::c_long;
pub type int_fast64_t = ::std::os::raw::c_long;
pub type uint_fast8_t = ::std::os::raw::c_uchar;
pub type uint_fast16_t = ::std::os::raw::c_ulong;
pub type uint_fast32_t = ::std::os::raw::c_ulong;
pub type uint_fast64_t = ::std::os::raw::c_ulong;
pub type intmax_t = __intmax_t;
pub type uintmax_t = __uintmax_t;
extern "C" {
    /// Return the MPV_CLIENT_API_VERSION the mpv source has been compiled with.
    pub fn mpv_client_api_version() -> ::std::os::raw::c_ulong;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_handle {
    _unused: [u8; 0],
}
/** No error happened (used to signal successful operation).
Keep in mind that many API functions returning error codes can also
return positive values, which also indicate success. API users can
hardcode the fact that ">= 0" means success.*/
pub const mpv_error_MPV_ERROR_SUCCESS: mpv_error = 0;
/** The event ringbuffer is full. This means the client is choked, and can't
receive any events. This can happen when too many asynchronous requests
have been made, but not answered. Probably never happens in practice,
unless the mpv core is frozen for some reason, and the client keeps
making asynchronous requests. (Bugs in the client API implementation
could also trigger this, e.g. if events become "lost".)*/
pub const mpv_error_MPV_ERROR_EVENT_QUEUE_FULL: mpv_error = -1;
/// Memory allocation failed.
pub const mpv_error_MPV_ERROR_NOMEM: mpv_error = -2;
/** The mpv core wasn't configured and initialized yet. See the notes in
mpv_create().*/
pub const mpv_error_MPV_ERROR_UNINITIALIZED: mpv_error = -3;
/** Generic catch-all error if a parameter is set to an invalid or
unsupported value. This is used if there is no better error code.*/
pub const mpv_error_MPV_ERROR_INVALID_PARAMETER: mpv_error = -4;
/// Trying to set an option that doesn't exist.
pub const mpv_error_MPV_ERROR_OPTION_NOT_FOUND: mpv_error = -5;
/// Trying to set an option using an unsupported MPV_FORMAT.
pub const mpv_error_MPV_ERROR_OPTION_FORMAT: mpv_error = -6;
/** Setting the option failed. Typically this happens if the provided option
value could not be parsed.*/
pub const mpv_error_MPV_ERROR_OPTION_ERROR: mpv_error = -7;
/// The accessed property doesn't exist.
pub const mpv_error_MPV_ERROR_PROPERTY_NOT_FOUND: mpv_error = -8;
/// Trying to set or get a property using an unsupported MPV_FORMAT.
pub const mpv_error_MPV_ERROR_PROPERTY_FORMAT: mpv_error = -9;
/** The property exists, but is not available. This usually happens when the
associated subsystem is not active, e.g. querying audio parameters while
audio is disabled.*/
pub const mpv_error_MPV_ERROR_PROPERTY_UNAVAILABLE: mpv_error = -10;
/// Error setting or getting a property.
pub const mpv_error_MPV_ERROR_PROPERTY_ERROR: mpv_error = -11;
/// General error when running a command with mpv_command and similar.
pub const mpv_error_MPV_ERROR_COMMAND: mpv_error = -12;
/// Generic error on loading (usually used with mpv_event_end_file.error).
pub const mpv_error_MPV_ERROR_LOADING_FAILED: mpv_error = -13;
/// Initializing the audio output failed.
pub const mpv_error_MPV_ERROR_AO_INIT_FAILED: mpv_error = -14;
/// Initializing the video output failed.
pub const mpv_error_MPV_ERROR_VO_INIT_FAILED: mpv_error = -15;
/** There was no audio or video data to play. This also happens if the
file was recognized, but did not contain any audio or video streams,
or no streams were selected.*/
pub const mpv_error_MPV_ERROR_NOTHING_TO_PLAY: mpv_error = -16;
/** When trying to load the file, the file format could not be determined,
or the file was too broken to open it.*/
pub const mpv_error_MPV_ERROR_UNKNOWN_FORMAT: mpv_error = -17;
/** Generic error for signaling that certain system requirements are not
fulfilled.*/
pub const mpv_error_MPV_ERROR_UNSUPPORTED: mpv_error = -18;
/// The API function which was called is a stub only.
pub const mpv_error_MPV_ERROR_NOT_IMPLEMENTED: mpv_error = -19;
/// Unspecified error.
pub const mpv_error_MPV_ERROR_GENERIC: mpv_error = -20;
/** List of error codes than can be returned by API functions. 0 and positive
return values always mean success, negative values are always errors.*/
pub type mpv_error = ::std::os::raw::c_int;
extern "C" {
    /** Return a string describing the error. For unknown errors, the string
    "unknown error" is returned.

    @param error error number, see enum mpv_error
    @return A static string describing the error. The string is completely
            static, i.e. doesn't need to be deallocated, and is valid forever.*/
    pub fn mpv_error_string(error: ::std::os::raw::c_int) -> *const ::std::os::raw::c_char;
}
extern "C" {
    /** General function to deallocate memory returned by some of the API functions.
    Call this only if it's explicitly documented as allowed. Calling this on
    mpv memory not owned by the caller will lead to undefined behavior.

    @param data A valid pointer returned by the API, or NULL.*/
    pub fn mpv_free(data: *mut ::std::os::raw::c_void);
}
extern "C" {
    /** Return the name of this client handle. Every client has its own unique
    name, which is mostly used for user interface purposes.

    @return The client name. The string is read-only and is valid until the
            mpv_handle is destroyed.*/
    pub fn mpv_client_name(ctx: *mut mpv_handle) -> *const ::std::os::raw::c_char;
}
extern "C" {
    /** Return the ID of this client handle. Every client has its own unique ID. This
    ID is never reused by the core, even if the mpv_handle at hand gets destroyed
    and new handles get allocated.

    IDs are never 0 or negative.

    Some mpv APIs (not necessarily all) accept a name in the form "@<id>" in
    addition of the proper mpv_client_name(), where "<id>" is the ID in decimal
    form (e.g. "@123"). For example, the "script-message-to" command takes the
    client name as first argument, but also accepts the client ID formatted in
    this manner.

    @return The client ID.*/
    pub fn mpv_client_id(ctx: *mut mpv_handle) -> i64;
}
extern "C" {
    /** Create a new mpv instance and an associated client API handle to control
    the mpv instance. This instance is in a pre-initialized state,
    and needs to be initialized to be actually used with most other API
    functions.

    Some API functions will return MPV_ERROR_UNINITIALIZED in the uninitialized
    state. You can call mpv_set_property() (or mpv_set_property_string() and
    other variants, and before mpv 0.21.0 mpv_set_option() etc.) to set initial
    options. After this, call mpv_initialize() to start the player, and then use
    e.g. mpv_command() to start playback of a file.

    The point of separating handle creation and actual initialization is that
    you can configure things which can't be changed during runtime.

    Unlike the command line player, this will have initial settings suitable
    for embedding in applications. The following settings are different:
    - stdin/stdout/stderr and the terminal will never be accessed. This is
      equivalent to setting the --no-terminal option.
      (Technically, this also suppresses C signal handling.)
    - No config files will be loaded. This is roughly equivalent to using
      --config=no. Since libmpv 1.15, you can actually re-enable this option,
      which will make libmpv load config files during mpv_initialize(). If you
      do this, you are strongly encouraged to set the "config-dir" option too.
      (Otherwise it will load the mpv command line player's config.)
      For example:
         mpv_set_option_string(mpv, "config-dir", "/my/path"); // set config root
         mpv_set_option_string(mpv, "config", "yes"); // enable config loading
         (call mpv_initialize() _after_ this)
    - Idle mode is enabled, which means the playback core will enter idle mode
      if there are no more files to play on the internal playlist, instead of
      exiting. This is equivalent to the --idle option.
    - Disable parts of input handling.
    - Most of the different settings can be viewed with the command line player
      by running "mpv --show-profile=libmpv".

    All this assumes that API users want a mpv instance that is strictly
    isolated from the command line player's configuration, user settings, and
    so on. You can re-enable disabled features by setting the appropriate
    options.

    The mpv command line parser is not available through this API, but you can
    set individual options with mpv_set_property(). Files for playback must be
    loaded with mpv_command() or others.

    Note that you should avoid doing concurrent accesses on the uninitialized
    client handle. (Whether concurrent access is definitely allowed or not has
    yet to be decided.)

    @return a new mpv client API handle. Returns NULL on error. Currently, this
            can happen in the following situations:
            - out of memory
            - LC_NUMERIC is not set to "C" (see general remarks)*/
    pub fn mpv_create() -> *mut mpv_handle;
}
extern "C" {
    /** Initialize an uninitialized mpv instance. If the mpv instance is already
    running, an error is returned.

    This function needs to be called to make full use of the client API if the
    client API handle was created with mpv_create().

    Only the following options are required to be set _before_ mpv_initialize():
         - options which are only read at initialization time:
           - config
           - config-dir
           - input-conf
           - load-scripts
           - script
           - player-operation-mode
           - input-app-events (OSX)
         - all encoding mode options

    @return error code*/
    pub fn mpv_initialize(ctx: *mut mpv_handle) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Disconnect and destroy the mpv_handle. ctx will be deallocated with this
    API call.

    If the last mpv_handle is detached, the core player is destroyed. In
    addition, if there are only weak mpv_handles (such as created by
    mpv_create_weak_client() or internal scripts), these mpv_handles will
    be sent MPV_EVENT_SHUTDOWN. This function may block until these clients
    have responded to the shutdown event, and the core is finally destroyed.*/
    pub fn mpv_destroy(ctx: *mut mpv_handle);
}
extern "C" {
    /** Similar to mpv_destroy(), but brings the player and all clients down
    as well, and waits until all of them are destroyed. This function blocks. The
    advantage over mpv_destroy() is that while mpv_destroy() merely
    detaches the client handle from the player, this function quits the player,
    waits until all other clients are destroyed (i.e. all mpv_handles are
    detached), and also waits for the final termination of the player.

    Since mpv_destroy() is called somewhere on the way, it's not safe to
    call other functions concurrently on the same context.

    Since mpv client API version 1.29:
     The first call on any mpv_handle will block until the core is destroyed.
     This means it will wait until other mpv_handle have been destroyed. If you
     want asynchronous destruction, just run the "quit" command, and then react
     to the MPV_EVENT_SHUTDOWN event.
     If another mpv_handle already called mpv_terminate_destroy(), this call will
     not actually block. It will destroy the mpv_handle, and exit immediately,
     while other mpv_handles might still be uninitializing.

    Before mpv client API version 1.29:
     If this is called on a mpv_handle that was not created with mpv_create(),
     this function will merely send a quit command and then call
     mpv_destroy(), without waiting for the actual shutdown.*/
    pub fn mpv_terminate_destroy(ctx: *mut mpv_handle);
}
extern "C" {
    /** Create a new client handle connected to the same player core as ctx. This
    context has its own event queue, its own mpv_request_event() state, its own
    mpv_request_log_messages() state, its own set of observed properties, and
    its own state for asynchronous operations. Otherwise, everything is shared.

    This handle should be destroyed with mpv_destroy() if no longer
    needed. The core will live as long as there is at least 1 handle referencing
    it. Any handle can make the core quit, which will result in every handle
    receiving MPV_EVENT_SHUTDOWN.

    This function can not be called before the main handle was initialized with
    mpv_initialize(). The new handle is always initialized, unless ctx=NULL was
    passed.

    @param ctx Used to get the reference to the mpv core; handle-specific
               settings and parameters are not used.
               If NULL, this function behaves like mpv_create() (ignores name).
    @param name The client name. This will be returned by mpv_client_name(). If
                the name is already in use, or contains non-alphanumeric
                characters (other than '_'), the name is modified to fit.
                If NULL, an arbitrary name is automatically chosen.
    @return a new handle, or NULL on error*/
    pub fn mpv_create_client(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
    ) -> *mut mpv_handle;
}
extern "C" {
    /** This is the same as mpv_create_client(), but the created mpv_handle is
    treated as a weak reference. If all mpv_handles referencing a core are
    weak references, the core is automatically destroyed. (This still goes
    through normal uninit of course. Effectively, if the last non-weak mpv_handle
    is destroyed, then the weak mpv_handles receive MPV_EVENT_SHUTDOWN and are
    asked to terminate as well.)

    Note if you want to use this like refcounting: you have to be aware that
    mpv_terminate_destroy() _and_ mpv_destroy() for the last non-weak
    mpv_handle will block until all weak mpv_handles are destroyed.*/
    pub fn mpv_create_weak_client(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
    ) -> *mut mpv_handle;
}
extern "C" {
    /** Load a config file. This loads and parses the file, and sets every entry in
    the config file's default section as if mpv_set_option_string() is called.

    The filename should be an absolute path. If it isn't, the actual path used
    is unspecified. (Note: an absolute path starts with '/' on UNIX.) If the
    file wasn't found, MPV_ERROR_INVALID_PARAMETER is returned.

    If a fatal error happens when parsing a config file, MPV_ERROR_OPTION_ERROR
    is returned. Errors when setting options as well as other types or errors
    are ignored (even if options do not exist). You can still try to capture
    the resulting error messages with mpv_request_log_messages(). Note that it's
    possible that some options were successfully set even if any of these errors
    happen.

    @param filename absolute path to the config file on the local filesystem
    @return error code*/
    pub fn mpv_load_config_file(
        ctx: *mut mpv_handle,
        filename: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Return the internal time in nanoseconds. This has an arbitrary start offset,
    but will never wrap or go backwards.

    Note that this is always the real time, and doesn't necessarily have to do
    with playback time. For example, playback could go faster or slower due to
    playback speed, or due to playback being paused. Use the "time-pos" property
    instead to get the playback status.

    Unlike other libmpv APIs, this can be called at absolutely any time (even
    within wakeup callbacks), as long as the context is valid.

    Safe to be called from mpv render API threads.*/
    pub fn mpv_get_time_ns(ctx: *mut mpv_handle) -> i64;
}
extern "C" {
    /// Same as mpv_get_time_ns but in microseconds.
    pub fn mpv_get_time_us(ctx: *mut mpv_handle) -> i64;
}
/** Invalid. Sometimes used for empty values. This is always defined to 0,
so a normal 0-init of mpv_format (or e.g. mpv_node) is guaranteed to set
this it to MPV_FORMAT_NONE (which makes some things saner as consequence).*/
pub const mpv_format_MPV_FORMAT_NONE: mpv_format = 0;
/** The basic type is char*. It returns the raw property string, like
 using ${=property} in input.conf (see input.rst).

 NULL isn't an allowed value.

 Warning: although the encoding is usually UTF-8, this is not always the
          case. File tags often store strings in some legacy codepage,
          and even filenames don't necessarily have to be in UTF-8 (at
          least on Linux). If you pass the strings to code that requires
          valid UTF-8, you have to sanitize it in some way.
          On Windows, filenames are always UTF-8, and libmpv converts
          between UTF-8 and UTF-16 when using win32 API functions. See
          the "Encoding of filenames" section for details.

 Example for reading:

     char *result = NULL;
     if (mpv_get_property(ctx, "property", MPV_FORMAT_STRING, &result) < 0)
         goto error;
     printf("%s\n", result);
     mpv_free(result);

 Or just use mpv_get_property_string().

 Example for writing:

     char *value = "the new value";
     // yep, you pass the address to the variable
     // (needed for symmetry with other types and mpv_get_property)
     mpv_set_property(ctx, "property", MPV_FORMAT_STRING, &value);

 Or just use mpv_set_property_string().
*/
pub const mpv_format_MPV_FORMAT_STRING: mpv_format = 1;
/** The basic type is char*. It returns the OSD property string, like
using ${property} in input.conf (see input.rst). In many cases, this
is the same as the raw string, but in other cases it's formatted for
display on OSD. It's intended to be human readable. Do not attempt to
parse these strings.

Only valid when doing read access. The rest works like MPV_FORMAT_STRING.*/
pub const mpv_format_MPV_FORMAT_OSD_STRING: mpv_format = 2;
/** The basic type is int. The only allowed values are 0 ("no")
and 1 ("yes").

Example for reading:

    int result;
    if (mpv_get_property(ctx, "property", MPV_FORMAT_FLAG, &result) < 0)
        goto error;
    printf("%s\n", result ? "true" : "false");

Example for writing:

    int flag = 1;
    mpv_set_property(ctx, "property", MPV_FORMAT_FLAG, &flag);*/
pub const mpv_format_MPV_FORMAT_FLAG: mpv_format = 3;
/// The basic type is int64_t.
pub const mpv_format_MPV_FORMAT_INT64: mpv_format = 4;
/// The basic type is double.
pub const mpv_format_MPV_FORMAT_DOUBLE: mpv_format = 5;
/** The type is mpv_node.

For reading, you usually would pass a pointer to a stack-allocated
mpv_node value to mpv, and when you're done you call
mpv_free_node_contents(&node).
You're expected not to write to the data - if you have to, copy it
first (which you have to do manually).

For writing, you construct your own mpv_node, and pass a pointer to the
API. The API will never write to your data (and copy it if needed), so
you're free to use any form of allocation or memory management you like.

Warning: when reading, always check the mpv_node.format member. For
         example, properties might change their type in future versions
         of mpv, or sometimes even during runtime.

Example for reading:

    mpv_node result;
    if (mpv_get_property(ctx, "property", MPV_FORMAT_NODE, &result) < 0)
        goto error;
    printf("format=%d\n", (int)result.format);
    mpv_free_node_contents(&result).

Example for writing:

    mpv_node value;
    value.format = MPV_FORMAT_STRING;
    value.u.string = "hello";
    mpv_set_property(ctx, "property", MPV_FORMAT_NODE, &value);*/
pub const mpv_format_MPV_FORMAT_NODE: mpv_format = 6;
/// Used with mpv_node only. Can usually not be used directly.
pub const mpv_format_MPV_FORMAT_NODE_ARRAY: mpv_format = 7;
/// See MPV_FORMAT_NODE_ARRAY.
pub const mpv_format_MPV_FORMAT_NODE_MAP: mpv_format = 8;
/** A raw, untyped byte array. Only used only with mpv_node, and only in
some very specific situations. (Some commands use it.)*/
pub const mpv_format_MPV_FORMAT_BYTE_ARRAY: mpv_format = 9;
/** Data format for options and properties. The API functions to get/set
properties and options support multiple formats, and this enum describes
them.*/
pub type mpv_format = ::std::os::raw::c_uint;
/** Generic data storage.

If mpv writes this struct (e.g. via mpv_get_property()), you must not change
the data. In some cases (mpv_get_property()), you have to free it with
mpv_free_node_contents(). If you fill this struct yourself, you're also
responsible for freeing it, and you must not call mpv_free_node_contents().*/
#[repr(C)]
#[derive(Copy, Clone)]
pub struct mpv_node {
    pub u: mpv_node__bindgen_ty_1,
    /** Type of the data stored in this struct. This value rules what members in
    the given union can be accessed. The following formats are currently
    defined to be allowed in mpv_node:

     MPV_FORMAT_STRING       (u.string)
     MPV_FORMAT_FLAG         (u.flag)
     MPV_FORMAT_INT64        (u.int64)
     MPV_FORMAT_DOUBLE       (u.double_)
     MPV_FORMAT_NODE_ARRAY   (u.list)
     MPV_FORMAT_NODE_MAP     (u.list)
     MPV_FORMAT_BYTE_ARRAY   (u.ba)
     MPV_FORMAT_NONE         (no member)

    If you encounter a value you don't know, you must not make any
    assumptions about the contents of union u.*/
    pub format: mpv_format,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union mpv_node__bindgen_ty_1 {
    pub string: *mut ::std::os::raw::c_char,
    /// valid if format==MPV_FORMAT_STRING
    pub flag: ::std::os::raw::c_int,
    /// valid if format==MPV_FORMAT_FLAG
    pub int64: i64,
    /// valid if format==MPV_FORMAT_INT64
    pub double_: f64,
    #[doc = " valid if format==MPV_FORMAT_DOUBLE */\n/**\n valid if format==MPV_FORMAT_NODE_ARRAY\n    or if format==MPV_FORMAT_NODE_MAP"]
    pub list: *mut mpv_node_list,
    /// valid if format==MPV_FORMAT_BYTE_ARRAY
    pub ba: *mut mpv_byte_array,
}
#[test]
fn bindgen_test_layout_mpv_node__bindgen_ty_1() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_node__bindgen_ty_1> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_node__bindgen_ty_1>(),
        8usize,
        concat!("Size of: ", stringify!(mpv_node__bindgen_ty_1)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_node__bindgen_ty_1>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_node__bindgen_ty_1)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).string) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(string),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).flag) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(flag),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).int64) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(int64),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).double_) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(double_),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).list) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(list),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).ba) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node__bindgen_ty_1),
            "::",
            stringify!(ba),
        ),
    );
}
impl ::std::fmt::Debug for mpv_node__bindgen_ty_1 {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        write!(f, "mpv_node__bindgen_ty_1 {{ union }}")
    }
}
#[test]
fn bindgen_test_layout_mpv_node() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_node> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_node>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_node)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_node>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_node)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).u) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node),
            "::",
            stringify!(u)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).format) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node),
            "::",
            stringify!(format)
        ),
    );
}
impl ::std::fmt::Debug for mpv_node {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        write!(
            f,
            "mpv_node {{ u: {:?}, format: {:?} }}",
            self.u, self.format
        )
    }
}
/// (see mpv_node)
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_node_list {
    /// Number of entries. Negative values are not allowed.
    pub num: ::std::os::raw::c_int,
    /** MPV_FORMAT_NODE_ARRAY:
     values[N] refers to value of the Nth item

    MPV_FORMAT_NODE_MAP:
     values[N] refers to value of the Nth key/value pair

    If num > 0, values[0] to values[num-1] (inclusive) are valid.
    Otherwise, this can be NULL.*/
    pub values: *mut mpv_node,
    /** MPV_FORMAT_NODE_ARRAY:
     unused (typically NULL), access is not allowed

    MPV_FORMAT_NODE_MAP:
     keys[N] refers to key of the Nth key/value pair. If num > 0, keys[0] to
     keys[num-1] (inclusive) are valid. Otherwise, this can be NULL.
     The keys are in random order. The only guarantee is that keys[N] belongs
     to the value values[N]. NULL keys are not allowed.*/
    pub keys: *mut *mut ::std::os::raw::c_char,
}
#[test]
fn bindgen_test_layout_mpv_node_list() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_node_list> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_node_list>(),
        24usize,
        concat!("Size of: ", stringify!(mpv_node_list)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_node_list>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_node_list)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).num) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node_list),
            "::",
            stringify!(num)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).values) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node_list),
            "::",
            stringify!(values)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).keys) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_node_list),
            "::",
            stringify!(keys)
        ),
    );
}
/// (see mpv_node)
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_byte_array {
    /** Pointer to the data. In what format the data is stored is up to whatever
    uses MPV_FORMAT_BYTE_ARRAY.*/
    pub data: *mut ::std::os::raw::c_void,
    /// Size of the data pointed to by ptr.
    pub size: usize,
}
#[test]
fn bindgen_test_layout_mpv_byte_array() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_byte_array> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_byte_array>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_byte_array)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_byte_array>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_byte_array)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).data) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_byte_array),
            "::",
            stringify!(data)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).size) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_byte_array),
            "::",
            stringify!(size)
        ),
    );
}
extern "C" {
    /** Frees any data referenced by the node. It doesn't free the node itself.
    Call this only if the mpv client API set the node. If you constructed the
    node yourself (manually), you have to free it yourself.

    If node->format is MPV_FORMAT_NONE, this call does nothing. Likewise, if
    the client API sets a node with this format, this function doesn't need to
    be called. (This is just a clarification that there's no danger of anything
    strange happening in these cases.)*/
    pub fn mpv_free_node_contents(node: *mut mpv_node);
}
extern "C" {
    /** Set an option. Note that you can't normally set options during runtime. It
    works in uninitialized state (see mpv_create()), and in some cases in at
    runtime.

    Using a format other than MPV_FORMAT_NODE is equivalent to constructing a
    mpv_node with the given format and data, and passing the mpv_node to this
    function.

    Note: this is semi-deprecated. For most purposes, this is not needed anymore.
          Starting with mpv version 0.21.0 (version 1.23) most options can be set
          with mpv_set_property() (and related functions), and even before
          mpv_initialize(). In some obscure corner cases, using this function
          to set options might still be required (see
          "Inconsistencies between options and properties" in the manpage). Once
          these are resolved, the option setting functions might be fully
          deprecated.

    @param name Option name. This is the same as on the mpv command line, but
                without the leading "--".
    @param format see enum mpv_format.
    @param[in] data Option value (according to the format).
    @return error code*/
    pub fn mpv_set_option(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
        data: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Convenience function to set an option to a string value. This is like
    calling mpv_set_option() with MPV_FORMAT_STRING.

    @return error code*/
    pub fn mpv_set_option_string(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
        data: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Send a command to the player. Commands are the same as those used in
    input.conf, except that this function takes parameters in a pre-split
    form.

    The commands and their parameters are documented in input.rst.

    Does not use OSD and string expansion by default (unlike mpv_command_string()
    and input.conf).

    @param[in] args NULL-terminated list of strings. Usually, the first item
                    is the command, and the following items are arguments.
    @return error code*/
    pub fn mpv_command(
        ctx: *mut mpv_handle,
        args: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Same as mpv_command(), but allows passing structured data in any format.
    In particular, calling mpv_command() is exactly like calling
    mpv_command_node() with the format set to MPV_FORMAT_NODE_ARRAY, and
    every arg passed in order as MPV_FORMAT_STRING.

    Does not use OSD and string expansion by default.

    The args argument can have one of the following formats:

    MPV_FORMAT_NODE_ARRAY:
         Positional arguments. Each entry is an argument using an arbitrary
         format (the format must be compatible to the used command). Usually,
         the first item is the command name (as MPV_FORMAT_STRING). The order
         of arguments is as documented in each command description.

    MPV_FORMAT_NODE_MAP:
         Named arguments. This requires at least an entry with the key "name"
         to be present, which must be a string, and contains the command name.
         The special entry "_flags" is optional, and if present, must be an
         array of strings, each being a command prefix to apply. All other
         entries are interpreted as arguments. They must use the argument names
         as documented in each command description. Some commands do not
         support named arguments at all, and must use MPV_FORMAT_NODE_ARRAY.

    @param[in] args mpv_node with format set to one of the values documented
                    above (see there for details)
    @param[out] result Optional, pass NULL if unused. If not NULL, and if the
                       function succeeds, this is set to command-specific return
                       data. You must call mpv_free_node_contents() to free it
                       (again, only if the command actually succeeds).
                       Not many commands actually use this at all.
    @return error code (the result parameter is not set on error)*/
    pub fn mpv_command_node(
        ctx: *mut mpv_handle,
        args: *mut mpv_node,
        result: *mut mpv_node,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** This is essentially identical to mpv_command() but it also returns a result.

    Does not use OSD and string expansion by default.

    @param[in] args NULL-terminated list of strings. Usually, the first item
                    is the command, and the following items are arguments.
    @param[out] result Optional, pass NULL if unused. If not NULL, and if the
                       function succeeds, this is set to command-specific return
                       data. You must call mpv_free_node_contents() to free it
                       (again, only if the command actually succeeds).
                       Not many commands actually use this at all.
    @return error code (the result parameter is not set on error)*/
    pub fn mpv_command_ret(
        ctx: *mut mpv_handle,
        args: *mut *const ::std::os::raw::c_char,
        result: *mut mpv_node,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Same as mpv_command, but use input.conf parsing for splitting arguments.
    This is slightly simpler, but also more error prone, since arguments may
    need quoting/escaping.

    This also has OSD and string expansion enabled by default.*/
    pub fn mpv_command_string(
        ctx: *mut mpv_handle,
        args: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Same as mpv_command, but run the command asynchronously.

    Commands are executed asynchronously. You will receive a
    MPV_EVENT_COMMAND_REPLY event. This event will also have an
    error code set if running the command failed. For commands that
    return data, the data is put into mpv_event_command.result.

    The only case when you do not receive an event is when the function call
    itself fails. This happens only if parsing the command itself (or otherwise
    validating it) fails, i.e. the return code of the API call is not 0 or
    positive.

    Safe to be called from mpv render API threads.

    @param reply_userdata the value mpv_event.reply_userdata of the reply will
                          be set to (see section about asynchronous calls)
    @param args NULL-terminated list of strings (see mpv_command())
    @return error code (if parsing or queuing the command fails)*/
    pub fn mpv_command_async(
        ctx: *mut mpv_handle,
        reply_userdata: u64,
        args: *mut *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Same as mpv_command_node(), but run it asynchronously. Basically, this
    function is to mpv_command_node() what mpv_command_async() is to
    mpv_command().

    See mpv_command_async() for details.

    Safe to be called from mpv render API threads.

    @param reply_userdata the value mpv_event.reply_userdata of the reply will
                          be set to (see section about asynchronous calls)
    @param args as in mpv_command_node()
    @return error code (if parsing or queuing the command fails)*/
    pub fn mpv_command_node_async(
        ctx: *mut mpv_handle,
        reply_userdata: u64,
        args: *mut mpv_node,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Signal to all async requests with the matching ID to abort. This affects
    the following API calls:

         mpv_command_async
         mpv_command_node_async

    All of these functions take a reply_userdata parameter. This API function
    tells all requests with the matching reply_userdata value to try to return
    as soon as possible. If there are multiple requests with matching ID, it
    aborts all of them.

    This API function is mostly asynchronous itself. It will not wait until the
    command is aborted. Instead, the command will terminate as usual, but with
    some work not done. How this is signaled depends on the specific command (for
    example, the "subprocess" command will indicate it by "killed_by_us" set to
    true in the result). How long it takes also depends on the situation. The
    aborting process is completely asynchronous.

    Not all commands may support this functionality. In this case, this function
    will have no effect. The same is true if the request using the passed
    reply_userdata has already terminated, has not been started yet, or was
    never in use at all.

    You have to be careful of race conditions: the time during which the abort
    request will be effective is _after_ e.g. mpv_command_async() has returned,
    and before the command has signaled completion with MPV_EVENT_COMMAND_REPLY.

    @param reply_userdata ID of the request to be aborted (see above)*/
    pub fn mpv_abort_async_command(ctx: *mut mpv_handle, reply_userdata: u64);
}
extern "C" {
    /** Set a property to a given value. Properties are essentially variables which
    can be queried or set at runtime. For example, writing to the pause property
    will actually pause or unpause playback.

    If the format doesn't match with the internal format of the property, access
    usually will fail with MPV_ERROR_PROPERTY_FORMAT. In some cases, the data
    is automatically converted and access succeeds. For example, MPV_FORMAT_INT64
    is always converted to MPV_FORMAT_DOUBLE, and access using MPV_FORMAT_STRING
    usually invokes a string parser. The same happens when calling this function
    with MPV_FORMAT_NODE: the underlying format may be converted to another
    type if possible.

    Using a format other than MPV_FORMAT_NODE is equivalent to constructing a
    mpv_node with the given format and data, and passing the mpv_node to this
    function. (Before API version 1.21, this was different.)

    Note: starting with mpv 0.21.0 (client API version 1.23), this can be used to
          set options in general. It even can be used before mpv_initialize()
          has been called. If called before mpv_initialize(), setting properties
          not backed by options will result in MPV_ERROR_PROPERTY_UNAVAILABLE.
          In some cases, properties and options still conflict. In these cases,
          mpv_set_property() accesses the options before mpv_initialize(), and
          the properties after mpv_initialize(). These conflicts will be removed
          in mpv 0.23.0. See mpv_set_option() for further remarks.

    @param name The property name. See input.rst for a list of properties.
    @param format see enum mpv_format.
    @param[in] data Option value.
    @return error code*/
    pub fn mpv_set_property(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
        data: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Convenience function to set a property to a string value.

    This is like calling mpv_set_property() with MPV_FORMAT_STRING.*/
    pub fn mpv_set_property_string(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
        data: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Convenience function to delete a property.

    This is equivalent to running the command "del [name]".

    @param name The property name. See input.rst for a list of properties.
    @return error code*/
    pub fn mpv_del_property(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Set a property asynchronously. You will receive the result of the operation
    as MPV_EVENT_SET_PROPERTY_REPLY event. The mpv_event.error field will contain
    the result status of the operation. Otherwise, this function is similar to
    mpv_set_property().

    Safe to be called from mpv render API threads.

    @param reply_userdata see section about asynchronous calls
    @param name The property name.
    @param format see enum mpv_format.
    @param[in] data Option value. The value will be copied by the function. It
                    will never be modified by the client API.
    @return error code if sending the request failed*/
    pub fn mpv_set_property_async(
        ctx: *mut mpv_handle,
        reply_userdata: u64,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
        data: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Read the value of the given property.

    If the format doesn't match with the internal format of the property, access
    usually will fail with MPV_ERROR_PROPERTY_FORMAT. In some cases, the data
    is automatically converted and access succeeds. For example, MPV_FORMAT_INT64
    is always converted to MPV_FORMAT_DOUBLE, and access using MPV_FORMAT_STRING
    usually invokes a string formatter.

    @param name The property name.
    @param format see enum mpv_format.
    @param[out] data Pointer to the variable holding the option value. On
                     success, the variable will be set to a copy of the option
                     value. For formats that require dynamic memory allocation,
                     you can free the value with mpv_free() (strings) or
                     mpv_free_node_contents() (MPV_FORMAT_NODE).
    @return error code*/
    pub fn mpv_get_property(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
        data: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Return the value of the property with the given name as string. This is
    equivalent to mpv_get_property() with MPV_FORMAT_STRING.

    See MPV_FORMAT_STRING for character encoding issues.

    On error, NULL is returned. Use mpv_get_property() if you want fine-grained
    error reporting.

    @param name The property name.
    @return Property value, or NULL if the property can't be retrieved. Free
            the string with mpv_free().*/
    pub fn mpv_get_property_string(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
    ) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    /** Return the property as "OSD" formatted string. This is the same as
    mpv_get_property_string, but using MPV_FORMAT_OSD_STRING.

    @return Property value, or NULL if the property can't be retrieved. Free
            the string with mpv_free().*/
    pub fn mpv_get_property_osd_string(
        ctx: *mut mpv_handle,
        name: *const ::std::os::raw::c_char,
    ) -> *mut ::std::os::raw::c_char;
}
extern "C" {
    /** Get a property asynchronously. You will receive the result of the operation
    as well as the property data with the MPV_EVENT_GET_PROPERTY_REPLY event.
    You should check the mpv_event.error field on the reply event.

    Safe to be called from mpv render API threads.

    @param reply_userdata see section about asynchronous calls
    @param name The property name.
    @param format see enum mpv_format.
    @return error code if sending the request failed*/
    pub fn mpv_get_property_async(
        ctx: *mut mpv_handle,
        reply_userdata: u64,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Get a notification whenever the given property changes. You will receive
    updates as MPV_EVENT_PROPERTY_CHANGE. Note that this is not very precise:
    for some properties, it may not send updates even if the property changed.
    This depends on the property, and it's a valid feature request to ask for
    better update handling of a specific property. (For some properties, like
    ``clock``, which shows the wall clock, this mechanism doesn't make too
    much sense anyway.)

    Property changes are coalesced: the change events are returned only once the
    event queue becomes empty (e.g. mpv_wait_event() would block or return
    MPV_EVENT_NONE), and then only one event per changed property is returned.

    You always get an initial change notification. This is meant to initialize
    the user's state to the current value of the property.

    Normally, change events are sent only if the property value changes according
    to the requested format. mpv_event_property will contain the property value
    as data member.

    Warning: if a property is unavailable or retrieving it caused an error,
             MPV_FORMAT_NONE will be set in mpv_event_property, even if the
             format parameter was set to a different value. In this case, the
             mpv_event_property.data field is invalid.

    If the property is observed with the format parameter set to MPV_FORMAT_NONE,
    you get low-level notifications whether the property _may_ have changed, and
    the data member in mpv_event_property will be unset. With this mode, you
    will have to determine yourself whether the property really changed. On the
    other hand, this mechanism can be faster and uses less resources.

    Observing a property that doesn't exist is allowed. (Although it may still
    cause some sporadic change events.)

    Keep in mind that you will get change notifications even if you change a
    property yourself. Try to avoid endless feedback loops, which could happen
    if you react to the change notifications triggered by your own change.

    Only the mpv_handle on which this was called will receive the property
    change events, or can unobserve them.

    Safe to be called from mpv render API threads.

    @param reply_userdata This will be used for the mpv_event.reply_userdata
                          field for the received MPV_EVENT_PROPERTY_CHANGE
                          events. (Also see section about asynchronous calls,
                          although this function is somewhat different from
                          actual asynchronous calls.)
                          If you have no use for this, pass 0.
                          Also see mpv_unobserve_property().
    @param name The property name.
    @param format see enum mpv_format. Can be MPV_FORMAT_NONE to omit values
                  from the change events.
    @return error code (usually fails only on OOM or unsupported format)*/
    pub fn mpv_observe_property(
        mpv: *mut mpv_handle,
        reply_userdata: u64,
        name: *const ::std::os::raw::c_char,
        format: mpv_format,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Undo mpv_observe_property(). This will remove all observed properties for
    which the given number was passed as reply_userdata to mpv_observe_property.

    Safe to be called from mpv render API threads.

    @param registered_reply_userdata ID that was passed to mpv_observe_property
    @return negative value is an error code, >=0 is number of removed properties
            on success (includes the case when 0 were removed)*/
    pub fn mpv_unobserve_property(
        mpv: *mut mpv_handle,
        registered_reply_userdata: u64,
    ) -> ::std::os::raw::c_int;
}
/// Nothing happened. Happens on timeouts or sporadic wakeups.
pub const mpv_event_id_MPV_EVENT_NONE: mpv_event_id = 0;
/** Happens when the player quits. The player enters a state where it tries
to disconnect all clients. Most requests to the player will fail, and
the client should react to this and quit with mpv_destroy() as soon as
possible.*/
pub const mpv_event_id_MPV_EVENT_SHUTDOWN: mpv_event_id = 1;
/// See mpv_request_log_messages().
pub const mpv_event_id_MPV_EVENT_LOG_MESSAGE: mpv_event_id = 2;
/** Reply to a mpv_get_property_async() request.
See also mpv_event and mpv_event_property.*/
pub const mpv_event_id_MPV_EVENT_GET_PROPERTY_REPLY: mpv_event_id = 3;
/** Reply to a mpv_set_property_async() request.
(Unlike MPV_EVENT_GET_PROPERTY, mpv_event_property is not used.)*/
pub const mpv_event_id_MPV_EVENT_SET_PROPERTY_REPLY: mpv_event_id = 4;
/** Reply to a mpv_command_async() or mpv_command_node_async() request.
See also mpv_event and mpv_event_command.*/
pub const mpv_event_id_MPV_EVENT_COMMAND_REPLY: mpv_event_id = 5;
/** Notification before playback start of a file (before the file is loaded).
See also mpv_event and mpv_event_start_file.*/
pub const mpv_event_id_MPV_EVENT_START_FILE: mpv_event_id = 6;
/** Notification after playback end (after the file was unloaded).
See also mpv_event and mpv_event_end_file.*/
pub const mpv_event_id_MPV_EVENT_END_FILE: mpv_event_id = 7;
/** Notification when the file has been loaded (headers were read etc.), and
decoding starts.*/
pub const mpv_event_id_MPV_EVENT_FILE_LOADED: mpv_event_id = 8;
/** Idle mode was entered. In this mode, no file is played, and the playback
core waits for new commands. (The command line player normally quits
instead of entering idle mode, unless --idle was specified. If mpv
was started with mpv_create(), idle mode is enabled by default.)

@deprecated This is equivalent to using mpv_observe_property() on the
            "idle-active" property. The event is redundant, and might be
            removed in the far future. As a further warning, this event
            is not necessarily sent at the right point anymore (at the
            start of the program), while the property behaves correctly.*/
pub const mpv_event_id_MPV_EVENT_IDLE: mpv_event_id = 11;
/** Sent every time after a video frame is displayed. Note that currently,
this will be sent in lower frequency if there is no video, or playback
is paused - but that will be removed in the future, and it will be
restricted to video frames only.

@deprecated Use mpv_observe_property() with relevant properties instead
            (such as "playback-time").*/
pub const mpv_event_id_MPV_EVENT_TICK: mpv_event_id = 14;
/** Triggered by the script-message input command. The command uses the
first argument of the command as client name (see mpv_client_name()) to
dispatch the message, and passes along all arguments starting from the
second argument as strings.
See also mpv_event and mpv_event_client_message.*/
pub const mpv_event_id_MPV_EVENT_CLIENT_MESSAGE: mpv_event_id = 16;
/** Happens after video changed in some way. This can happen on resolution
changes, pixel format changes, or video filter changes. The event is
sent after the video filters and the VO are reconfigured. Applications
embedding a mpv window should listen to this event in order to resize
the window if needed.
Note that this event can happen sporadically, and you should check
yourself whether the video parameters really changed before doing
something expensive.*/
pub const mpv_event_id_MPV_EVENT_VIDEO_RECONFIG: mpv_event_id = 17;
/** Similar to MPV_EVENT_VIDEO_RECONFIG. This is relatively uninteresting,
because there is no such thing as audio output embedding.*/
pub const mpv_event_id_MPV_EVENT_AUDIO_RECONFIG: mpv_event_id = 18;
/** Happens when a seek was initiated. Playback stops. Usually it will
resume with MPV_EVENT_PLAYBACK_RESTART as soon as the seek is finished.*/
pub const mpv_event_id_MPV_EVENT_SEEK: mpv_event_id = 20;
/** There was a discontinuity of some sort (like a seek), and playback
was reinitialized. Usually happens on start of playback and after
seeking. The main purpose is allowing the client to detect when a seek
request is finished.*/
pub const mpv_event_id_MPV_EVENT_PLAYBACK_RESTART: mpv_event_id = 21;
/** Event sent due to mpv_observe_property().
See also mpv_event and mpv_event_property.*/
pub const mpv_event_id_MPV_EVENT_PROPERTY_CHANGE: mpv_event_id = 22;
/** Happens if the internal per-mpv_handle ringbuffer overflows, and at
least 1 event had to be dropped. This can happen if the client doesn't
read the event queue quickly enough with mpv_wait_event(), or if the
client makes a very large number of asynchronous calls at once.

Event delivery will continue normally once this event was returned
(this forces the client to empty the queue completely).*/
pub const mpv_event_id_MPV_EVENT_QUEUE_OVERFLOW: mpv_event_id = 24;
/** Triggered if a hook handler was registered with mpv_hook_add(), and the
hook is invoked. If you receive this, you must handle it, and continue
the hook with mpv_hook_continue().
See also mpv_event and mpv_event_hook.*/
pub const mpv_event_id_MPV_EVENT_HOOK: mpv_event_id = 25;
pub type mpv_event_id = ::std::os::raw::c_uint;
extern "C" {
    /** Return a string describing the event. For unknown events, NULL is returned.

    Note that all events actually returned by the API will also yield a non-NULL
    string with this function.

    @param event event ID, see see enum mpv_event_id
    @return A static string giving a short symbolic name of the event. It
            consists of lower-case alphanumeric characters and can include "-"
            characters. This string is suitable for use in e.g. scripting
            interfaces.
            The string is completely static, i.e. doesn't need to be deallocated,
            and is valid forever.*/
    pub fn mpv_event_name(event: mpv_event_id) -> *const ::std::os::raw::c_char;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_property {
    /// Name of the property.
    pub name: *const ::std::os::raw::c_char,
    /** Format of the data field in the same struct. See enum mpv_format.
    This is always the same format as the requested format, except when
    the property could not be retrieved (unavailable, or an error happened),
    in which case the format is MPV_FORMAT_NONE.*/
    pub format: mpv_format,
    /** Received property value. Depends on the format. This is like the
    pointer argument passed to mpv_get_property().

    For example, for MPV_FORMAT_STRING you get the string with:

       char *value = *(char **)(event_property->data);

    Note that this is set to NULL if retrieving the property failed (the
    format will be MPV_FORMAT_NONE).*/
    pub data: *mut ::std::os::raw::c_void,
}
#[test]
fn bindgen_test_layout_mpv_event_property() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_property> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_property>(),
        24usize,
        concat!("Size of: ", stringify!(mpv_event_property)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_property>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_property)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).name) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_property),
            "::",
            stringify!(name),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).format) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_property),
            "::",
            stringify!(format),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).data) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_property),
            "::",
            stringify!(data),
        ),
    );
}
pub const mpv_log_level_MPV_LOG_LEVEL_NONE: mpv_log_level = 0;
/// "no"    - disable absolutely all messages
pub const mpv_log_level_MPV_LOG_LEVEL_FATAL: mpv_log_level = 10;
/// "fatal" - critical/aborting errors
pub const mpv_log_level_MPV_LOG_LEVEL_ERROR: mpv_log_level = 20;
/// "error" - simple errors
pub const mpv_log_level_MPV_LOG_LEVEL_WARN: mpv_log_level = 30;
/// "warn"  - possible problems
pub const mpv_log_level_MPV_LOG_LEVEL_INFO: mpv_log_level = 40;
/// "info"  - informational message
pub const mpv_log_level_MPV_LOG_LEVEL_V: mpv_log_level = 50;
/// "v"     - noisy informational message
pub const mpv_log_level_MPV_LOG_LEVEL_DEBUG: mpv_log_level = 60;
/// "debug" - very noisy technical information
pub const mpv_log_level_MPV_LOG_LEVEL_TRACE: mpv_log_level = 70;
/** Numeric log levels. The lower the number, the more important the message is.
MPV_LOG_LEVEL_NONE is never used when receiving messages. The string in
the comment after the value is the name of the log level as used for the
mpv_request_log_messages() function.
Unused numeric values are unused, but reserved for future use.*/
pub type mpv_log_level = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_log_message {
    /** The module prefix, identifies the sender of the message. As a special
    case, if the message buffer overflows, this will be set to the string
    "overflow" (which doesn't appear as prefix otherwise), and the text
    field will contain an informative message.*/
    pub prefix: *const ::std::os::raw::c_char,
    /** The log level as string. See mpv_request_log_messages() for possible
    values. The level "no" is never used here.*/
    pub level: *const ::std::os::raw::c_char,
    /** The log message. It consists of 1 line of text, and is terminated with
    a newline character. (Before API version 1.6, it could contain multiple
    or partial lines.)*/
    pub text: *const ::std::os::raw::c_char,
    /** The same contents as the level field, but as a numeric ID.
    Since API version 1.6.*/
    pub log_level: mpv_log_level,
}
#[test]
fn bindgen_test_layout_mpv_event_log_message() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_log_message> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_log_message>(),
        32usize,
        concat!("Size of: ", stringify!(mpv_event_log_message)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_log_message>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_log_message)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).prefix) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_log_message),
            "::",
            stringify!(prefix),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).level) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_log_message),
            "::",
            stringify!(level),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).text) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_log_message),
            "::",
            stringify!(text),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).log_level) as usize - ptr as usize },
        24usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_log_message),
            "::",
            stringify!(log_level),
        ),
    );
}
/** The end of file was reached. Sometimes this may also happen on
incomplete or corrupted files, or if the network connection was
interrupted when playing a remote file. It also happens if the
playback range was restricted with --end or --frames or similar.*/
pub const mpv_end_file_reason_MPV_END_FILE_REASON_EOF: mpv_end_file_reason = 0;
/// Playback was stopped by an external action (e.g. playlist controls).
pub const mpv_end_file_reason_MPV_END_FILE_REASON_STOP: mpv_end_file_reason = 2;
/// Playback was stopped by the quit command or player shutdown.
pub const mpv_end_file_reason_MPV_END_FILE_REASON_QUIT: mpv_end_file_reason = 3;
/** Some kind of error happened that lead to playback abort. Does not
necessarily happen on incomplete or broken files (in these cases, both
MPV_END_FILE_REASON_ERROR or MPV_END_FILE_REASON_EOF are possible).

mpv_event_end_file.error will be set.*/
pub const mpv_end_file_reason_MPV_END_FILE_REASON_ERROR: mpv_end_file_reason = 4;
/** The file was a playlist or similar. When the playlist is read, its
entries will be appended to the playlist after the entry of the current
file, the entry of the current file is removed, and a MPV_EVENT_END_FILE
event is sent with reason set to MPV_END_FILE_REASON_REDIRECT. Then
playback continues with the playlist contents.
Since API version 1.18.*/
pub const mpv_end_file_reason_MPV_END_FILE_REASON_REDIRECT: mpv_end_file_reason = 5;
/// Since API version 1.9.
pub type mpv_end_file_reason = ::std::os::raw::c_uint;
/// Since API version 1.108.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_start_file {
    /// Playlist entry ID of the file being loaded now.
    pub playlist_entry_id: i64,
}
#[test]
fn bindgen_test_layout_mpv_event_start_file() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_start_file> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_start_file>(),
        8usize,
        concat!("Size of: ", stringify!(mpv_event_start_file)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_start_file>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_start_file)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).playlist_entry_id) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_start_file),
            "::",
            stringify!(playlist_entry_id),
        ),
    );
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_end_file {
    /** Corresponds to the values in enum mpv_end_file_reason.

    Unknown values should be treated as unknown.*/
    pub reason: mpv_end_file_reason,
    /** If reason==MPV_END_FILE_REASON_ERROR, this contains a mpv error code
    (one of MPV_ERROR_...) giving an approximate reason why playback
    failed. In other cases, this field is 0 (no error).
    Since API version 1.9.*/
    pub error: ::std::os::raw::c_int,
    /** Playlist entry ID of the file that was being played or attempted to be
    played. This has the same value as the playlist_entry_id field in the
    corresponding mpv_event_start_file event.
    Since API version 1.108.*/
    pub playlist_entry_id: i64,
    /** If loading ended, because the playlist entry to be played was for example
    a playlist, and the current playlist entry is replaced with a number of
    other entries. This may happen at least with MPV_END_FILE_REASON_REDIRECT
    (other event types may use this for similar but different purposes in the
    future). In this case, playlist_insert_id will be set to the playlist
    entry ID of the first inserted entry, and playlist_insert_num_entries to
    the total number of inserted playlist entries. Note this in this specific
    case, the ID of the last inserted entry is playlist_insert_id+num-1.
    Beware that depending on circumstances, you may observe the new playlist
    entries before seeing the event (e.g. reading the "playlist" property or
    getting a property change notification before receiving the event).
    Since API version 1.108.*/
    pub playlist_insert_id: i64,
    /** See playlist_insert_id. Only non-0 if playlist_insert_id is valid. Never
    negative.
    Since API version 1.108.*/
    pub playlist_insert_num_entries: ::std::os::raw::c_int,
}
#[test]
fn bindgen_test_layout_mpv_event_end_file() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_end_file> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_end_file>(),
        32usize,
        concat!("Size of: ", stringify!(mpv_event_end_file)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_end_file>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_end_file)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).reason) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_end_file),
            "::",
            stringify!(reason),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).error) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_end_file),
            "::",
            stringify!(error),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).playlist_entry_id) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_end_file),
            "::",
            stringify!(playlist_entry_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).playlist_insert_id) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_end_file),
            "::",
            stringify!(playlist_insert_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).playlist_insert_num_entries) as usize - ptr as usize },
        24usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_end_file),
            "::",
            stringify!(playlist_insert_num_entries),
        ),
    );
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_client_message {
    /** Arbitrary arguments chosen by the sender of the message. If num_args > 0,
    you can access args[0] through args[num_args - 1] (inclusive). What
    these arguments mean is up to the sender and receiver.
    None of the valid items are NULL.*/
    pub num_args: ::std::os::raw::c_int,
    pub args: *mut *const ::std::os::raw::c_char,
}
#[test]
fn bindgen_test_layout_mpv_event_client_message() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_client_message> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_client_message>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_event_client_message)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_client_message>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_client_message)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).num_args) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_client_message),
            "::",
            stringify!(num_args),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).args) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_client_message),
            "::",
            stringify!(args),
        ),
    );
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event_hook {
    /// The hook name as passed to mpv_hook_add().
    pub name: *const ::std::os::raw::c_char,
    /// Internal ID that must be passed to mpv_hook_continue().
    pub id: u64,
}
#[test]
fn bindgen_test_layout_mpv_event_hook() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_hook> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_hook>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_event_hook)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_hook>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_hook)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).name) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_hook),
            "::",
            stringify!(name)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).id) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_hook),
            "::",
            stringify!(id)
        ),
    );
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct mpv_event_command {
    /** Result data of the command. Note that success/failure is signaled
    separately via mpv_event.error. This field is only for result data
    in case of success. Most commands leave it at MPV_FORMAT_NONE. Set
    to MPV_FORMAT_NONE on failure.*/
    pub result: mpv_node,
}
#[test]
fn bindgen_test_layout_mpv_event_command() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event_command> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event_command>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_event_command)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event_command>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event_command)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).result) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event_command),
            "::",
            stringify!(result),
        ),
    );
}
impl ::std::fmt::Debug for mpv_event_command {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        write!(f, "mpv_event_command {{ result: {:?} }}", self.result)
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_event {
    /** One of mpv_event. Keep in mind that later ABI compatible releases might
    add new event types. These should be ignored by the API user.*/
    pub event_id: mpv_event_id,
    /** This is mainly used for events that are replies to (asynchronous)
    requests. It contains a status code, which is >= 0 on success, or < 0
    on error (a mpv_error value). Usually, this will be set if an
    asynchronous request fails.
    Used for:
     MPV_EVENT_GET_PROPERTY_REPLY
     MPV_EVENT_SET_PROPERTY_REPLY
     MPV_EVENT_COMMAND_REPLY*/
    pub error: ::std::os::raw::c_int,
    /** If the event is in reply to a request (made with this API and this
    API handle), this is set to the reply_userdata parameter of the request
    call. Otherwise, this field is 0.
    Used for:
     MPV_EVENT_GET_PROPERTY_REPLY
     MPV_EVENT_SET_PROPERTY_REPLY
     MPV_EVENT_COMMAND_REPLY
     MPV_EVENT_PROPERTY_CHANGE
     MPV_EVENT_HOOK*/
    pub reply_userdata: u64,
    /** The meaning and contents of the data member depend on the event_id:
     MPV_EVENT_GET_PROPERTY_REPLY:     mpv_event_property*
     MPV_EVENT_PROPERTY_CHANGE:        mpv_event_property*
     MPV_EVENT_LOG_MESSAGE:            mpv_event_log_message*
     MPV_EVENT_CLIENT_MESSAGE:         mpv_event_client_message*
     MPV_EVENT_START_FILE:             mpv_event_start_file* (since v1.108)
     MPV_EVENT_END_FILE:               mpv_event_end_file*
     MPV_EVENT_HOOK:                   mpv_event_hook*
     MPV_EVENT_COMMAND_REPLY*          mpv_event_command*
     other: NULL

    Note: future enhancements might add new event structs for existing or new
          event types.*/
    pub data: *mut ::std::os::raw::c_void,
}
#[test]
fn bindgen_test_layout_mpv_event() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_event> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_event>(),
        24usize,
        concat!("Size of: ", stringify!(mpv_event)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_event>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_event)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).event_id) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event),
            "::",
            stringify!(event_id)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).error) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event),
            "::",
            stringify!(error)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).reply_userdata) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event),
            "::",
            stringify!(reply_userdata),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).data) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_event),
            "::",
            stringify!(data)
        ),
    );
}
extern "C" {
    /** Convert the given src event to a mpv_node, and set *dst to the result. *dst
    is set to a MPV_FORMAT_NODE_MAP, with fields for corresponding mpv_event and
    mpv_event.data/mpv_event_* fields.

    The exact details are not completely documented out of laziness. A start
    is located in the "Events" section of the manpage.

    *dst may point to newly allocated memory, or pointers in mpv_event. You must
    copy the entire mpv_node if you want to reference it after mpv_event becomes
    invalid (such as making a new mpv_wait_event() call, or destroying the
    mpv_handle from which it was returned). Call mpv_free_node_contents() to free
    any memory allocations made by this API function.

    Safe to be called from mpv render API threads.

    @param dst Target. This is not read and fully overwritten. Must be released
               with mpv_free_node_contents(). Do not write to pointers returned
               by it. (On error, this may be left as an empty node.)
    @param src The source event. Not modified (it's not const due to the author's
               prejudice of the C version of const).
    @return error code (MPV_ERROR_NOMEM only, if at all)*/
    pub fn mpv_event_to_node(dst: *mut mpv_node, src: *mut mpv_event) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Enable or disable the given event.

    Some events are enabled by default. Some events can't be disabled.

    (Informational note: currently, all events are enabled by default, except
     MPV_EVENT_TICK.)

    Safe to be called from mpv render API threads.

    @param event See enum mpv_event_id.
    @param enable 1 to enable receiving this event, 0 to disable it.
    @return error code*/
    pub fn mpv_request_event(
        ctx: *mut mpv_handle,
        event: mpv_event_id,
        enable: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Enable or disable receiving of log messages. These are the messages the
    command line player prints to the terminal. This call sets the minimum
    required log level for a message to be received with MPV_EVENT_LOG_MESSAGE.

    @param min_level Minimal log level as string. Valid log levels:
                         no fatal error warn info v debug trace
                     The value "no" disables all messages. This is the default.
                     An exception is the value "terminal-default", which uses the
                     log level as set by the "--msg-level" option. This works
                     even if the terminal is disabled. (Since API version 1.19.)
                     Also see mpv_log_level.
    @return error code*/
    pub fn mpv_request_log_messages(
        ctx: *mut mpv_handle,
        min_level: *const ::std::os::raw::c_char,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Wait for the next event, or until the timeout expires, or if another thread
    makes a call to mpv_wakeup(). Passing 0 as timeout will never wait, and
    is suitable for polling.

    The internal event queue has a limited size (per client handle). If you
    don't empty the event queue quickly enough with mpv_wait_event(), it will
    overflow and silently discard further events. If this happens, making
    asynchronous requests will fail as well (with MPV_ERROR_EVENT_QUEUE_FULL).

    Only one thread is allowed to call this on the same mpv_handle at a time.
    The API won't complain if more than one thread calls this, but it will cause
    race conditions in the client when accessing the shared mpv_event struct.
    Note that most other API functions are not restricted by this, and no API
    function internally calls mpv_wait_event(). Additionally, concurrent calls
    to different mpv_handles are always safe.

    As long as the timeout is 0, this is safe to be called from mpv render API
    threads.

    @param timeout Timeout in seconds, after which the function returns even if
                   no event was received. A MPV_EVENT_NONE is returned on
                   timeout. A value of 0 will disable waiting. Negative values
                   will wait with an infinite timeout.
    @return A struct containing the event ID and other data. The pointer (and
            fields in the struct) stay valid until the next mpv_wait_event()
            call, or until the mpv_handle is destroyed. You must not write to
            the struct, and all memory referenced by it will be automatically
            released by the API on the next mpv_wait_event() call, or when the
            context is destroyed. The return value is never NULL.*/
    pub fn mpv_wait_event(ctx: *mut mpv_handle, timeout: f64) -> *mut mpv_event;
}
extern "C" {
    /** Interrupt the current mpv_wait_event() call. This will wake up the thread
    currently waiting in mpv_wait_event(). If no thread is waiting, the next
    mpv_wait_event() call will return immediately (this is to avoid lost
    wakeups).

    mpv_wait_event() will receive a MPV_EVENT_NONE if it's woken up due to
    this call. But note that this dummy event might be skipped if there are
    already other events queued. All what counts is that the waiting thread
    is woken up at all.

    Safe to be called from mpv render API threads.*/
    pub fn mpv_wakeup(ctx: *mut mpv_handle);
}
extern "C" {
    /** Set a custom function that should be called when there are new events. Use
    this if blocking in mpv_wait_event() to wait for new events is not feasible.

    Keep in mind that the callback will be called from foreign threads. You
    must not make any assumptions of the environment, and you must return as
    soon as possible (i.e. no long blocking waits). Exiting the callback through
    any other means than a normal return is forbidden (no throwing exceptions,
    no longjmp() calls). You must not change any local thread state (such as
    the C floating point environment).

    You are not allowed to call any client API functions inside of the callback.
    In particular, you should not do any processing in the callback, but wake up
    another thread that does all the work. The callback is meant strictly for
    notification only, and is called from arbitrary core parts of the player,
    that make no considerations for reentrant API use or allowing the callee to
    spend a lot of time doing other things. Keep in mind that it's also possible
    that the callback is called from a thread while a mpv API function is called
    (i.e. it can be reentrant).

    In general, the client API expects you to call mpv_wait_event() to receive
    notifications, and the wakeup callback is merely a helper utility to make
    this easier in certain situations. Note that it's possible that there's
    only one wakeup callback invocation for multiple events. You should call
    mpv_wait_event() with no timeout until MPV_EVENT_NONE is reached, at which
    point the event queue is empty.

    If you actually want to do processing in a callback, spawn a thread that
    does nothing but call mpv_wait_event() in a loop and dispatches the result
    to a callback.

    Only one wakeup callback can be set.

    @param cb function that should be called if a wakeup is required
    @param d arbitrary userdata passed to cb*/
    pub fn mpv_set_wakeup_callback(
        ctx: *mut mpv_handle,
        cb: ::std::option::Option<unsafe extern "C" fn(d: *mut ::std::os::raw::c_void)>,
        d: *mut ::std::os::raw::c_void,
    );
}
extern "C" {
    /** Block until all asynchronous requests are done. This affects functions like
    mpv_command_async(), which return immediately and return their result as
    events.

    This is a helper, and somewhat equivalent to calling mpv_wait_event() in a
    loop until all known asynchronous requests have sent their reply as event,
    except that the event queue is not emptied.

    In case you called mpv_suspend() before, this will also forcibly reset the
    suspend counter of the given handle.*/
    pub fn mpv_wait_async_requests(ctx: *mut mpv_handle);
}
extern "C" {
    /** A hook is like a synchronous event that blocks the player. You register
    a hook handler with this function. You will get an event, which you need
    to handle, and once things are ready, you can let the player continue with
    mpv_hook_continue().

    Currently, hooks can't be removed explicitly. But they will be implicitly
    removed if the mpv_handle it was registered with is destroyed. This also
    continues the hook if it was being handled by the destroyed mpv_handle (but
    this should be avoided, as it might mess up order of hook execution).

    Hook handlers are ordered globally by priority and order of registration.
    Handlers for the same hook with same priority are invoked in order of
    registration (the handler registered first is run first). Handlers with
    lower priority are run first (which seems backward).

    See the "Hooks" section in the manpage to see which hooks are currently
    defined.

    Some hooks might be reentrant (so you get multiple MPV_EVENT_HOOK for the
    same hook). If this can happen for a specific hook type, it will be
    explicitly documented in the manpage.

    Only the mpv_handle on which this was called will receive the hook events,
    or can "continue" them.

    @param reply_userdata This will be used for the mpv_event.reply_userdata
                          field for the received MPV_EVENT_HOOK events.
                          If you have no use for this, pass 0.
    @param name The hook name. This should be one of the documented names. But
                if the name is unknown, the hook event will simply be never
                raised.
    @param priority See remarks above. Use 0 as a neutral default.
    @return error code (usually fails only on OOM)*/
    pub fn mpv_hook_add(
        ctx: *mut mpv_handle,
        reply_userdata: u64,
        name: *const ::std::os::raw::c_char,
        priority: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Respond to a MPV_EVENT_HOOK event. You must call this after you have handled
    the event. There is no way to "cancel" or "stop" the hook.

    Calling this will will typically unblock the player for whatever the hook
    is responsible for (e.g. for the "on_load" hook it lets it continue
    playback).

    It is explicitly undefined behavior to call this more than once for each
    MPV_EVENT_HOOK, to pass an incorrect ID, or to call this on a mpv_handle
    different from the one that registered the handler and received the event.

    @param id This must be the value of the mpv_event_hook.id field for the
              corresponding MPV_EVENT_HOOK.
    @return error code*/
    pub fn mpv_hook_continue(ctx: *mut mpv_handle, id: u64) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Return a UNIX file descriptor referring to the read end of a pipe. This
    pipe can be used to wake up a poll() based processing loop. The purpose of
    this function is very similar to mpv_set_wakeup_callback(), and provides
    a primitive mechanism to handle coordinating a foreign event loop and the
    libmpv event loop. The pipe is non-blocking. It's closed when the mpv_handle
    is destroyed. This function always returns the same value (on success).

    This is in fact implemented using the same underlying code as for
    mpv_set_wakeup_callback() (though they don't conflict), and it is as if each
    callback invocation writes a single 0 byte to the pipe. When the pipe
    becomes readable, the code calling poll() (or select()) on the pipe should
    read all contents of the pipe and then call mpv_wait_event(c, 0) until
    no new events are returned. The pipe contents do not matter and can just
    be discarded. There is not necessarily one byte per readable event in the
    pipe. For example, the pipes are non-blocking, and mpv won't block if the
    pipe is full. Pipes are normally limited to 4096 bytes, so if there are
    more than 4096 events, the number of readable bytes can not equal the number
    of events queued. Also, it's possible that mpv does not write to the pipe
    once it's guaranteed that the client was already signaled. See the example
    below how to do it correctly.

    Example:

     int pipefd = mpv_get_wakeup_pipe(mpv);
     if (pipefd < 0)
         error();
     while (1) {
         struct pollfd pfds[1] = {
             { .fd = pipefd, .events = POLLIN },
         };
         // Wait until there are possibly new mpv events.
         poll(pfds, 1, -1);
         if (pfds[0].revents & POLLIN) {
             // Empty the pipe. Doing this before calling mpv_wait_event()
             // ensures that no wakeups are missed. It's not so important to
             // make sure the pipe is really empty (it will just cause some
             // additional wakeups in unlikely corner cases).
             char unused[256];
             read(pipefd, unused, sizeof(unused));
             while (1) {
                 mpv_event *ev = mpv_wait_event(mpv, 0);
                 // If MPV_EVENT_NONE is received, the event queue is empty.
                 if (ev->event_id == MPV_EVENT_NONE)
                     break;
                 // Process the event.
                 ...
             }
         }
     }

    @deprecated this function will be removed in the future. If you need this
                functionality, use mpv_set_wakeup_callback(), create a pipe
                manually, and call write() on your pipe in the callback.

    @return A UNIX FD of the read end of the wakeup pipe, or -1 on error.
            On MS Windows/MinGW, this will always return -1.*/
    pub fn mpv_get_wakeup_pipe(ctx: *mut mpv_handle) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_render_context {
    _unused: [u8; 0],
}
/** Not a valid value, but also used to terminate a params array. Its value
is always guaranteed to be 0 (even if the ABI changes in the future).*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_INVALID: mpv_render_param_type = 0;
/** The render API to use. Valid for mpv_render_context_create().

Type: char*

Defined APIs:

  MPV_RENDER_API_TYPE_OPENGL:
     OpenGL desktop 2.1 or later (preferably core profile compatible to
     OpenGL 3.2), or OpenGLES 2.0 or later.
     Providing MPV_RENDER_PARAM_OPENGL_INIT_PARAMS is required.
     It is expected that an OpenGL context is valid and "current" when
     calling mpv_render_* functions (unless specified otherwise). It
     must be the same context for the same mpv_render_context.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_API_TYPE: mpv_render_param_type = 1;
/** Required parameters for initializing the OpenGL renderer. Valid for
mpv_render_context_create().
Type: mpv_opengl_init_params**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_OPENGL_INIT_PARAMS: mpv_render_param_type = 2;
/** Describes a GL render target. Valid for mpv_render_context_render().
Type: mpv_opengl_fbo**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_OPENGL_FBO: mpv_render_param_type = 3;
/** Control flipped rendering. Valid for mpv_render_context_render().
Type: int*
If the value is set to 0, render normally. Otherwise, render it flipped,
which is needed e.g. when rendering to an OpenGL default framebuffer
(which has a flipped coordinate system).*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_FLIP_Y: mpv_render_param_type = 4;
/** Control surface depth. Valid for mpv_render_context_render().
Type: int*
This implies the depth of the surface passed to the render function in
bits per channel. If omitted or set to 0, the renderer will assume 8.
Typically used to control dithering.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_DEPTH: mpv_render_param_type = 5;
/** ICC profile blob. Valid for mpv_render_context_set_parameter().
Type: mpv_byte_array*
Set an ICC profile for use with the "icc-profile-auto" option. (If the
option is not enabled, the ICC data will not be used.)*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_ICC_PROFILE: mpv_render_param_type = 6;
/** Ambient light in lux. Valid for mpv_render_context_set_parameter().
Type: int*
This can be used for automatic gamma correction.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_AMBIENT_LIGHT: mpv_render_param_type = 7;
/** X11 Display, sometimes used for hwdec. Valid for
mpv_render_context_create(). The Display must stay valid for the lifetime
of the mpv_render_context.
Type: Display**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_X11_DISPLAY: mpv_render_param_type = 8;
/** Wayland display, sometimes used for hwdec. Valid for
mpv_render_context_create(). The wl_display must stay valid for the
lifetime of the mpv_render_context.
Type: struct wl_display**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_WL_DISPLAY: mpv_render_param_type = 9;
/** Better control about rendering and enabling some advanced features. Valid
for mpv_render_context_create().

This conflates multiple requirements the API user promises to abide if
this option is enabled:

 - The API user's render thread, which is calling the mpv_render_*()
   functions, never waits for the core. Otherwise deadlocks can happen.
   See "Threading" section.
 - The callback set with mpv_render_context_set_update_callback() can now
   be called even if there is no new frame. The API user should call the
   mpv_render_context_update() function, and interpret the return value
   for whether a new frame should be rendered.
 - Correct functionality is impossible if the update callback is not set,
   or not set soon enough after mpv_render_context_create() (the core can
   block while waiting for you to call mpv_render_context_update(), and
   if the update callback is not correctly set, it will deadlock, or
   block for too long).

In general, setting this option will enable the following features (and
possibly more):

 - "Direct rendering", which means the player decodes directly to a
   texture, which saves a copy per video frame ("vd-lavc-dr" option
   needs to be enabled, and the rendering backend as well as the
   underlying GPU API/driver needs to have support for it).
 - Rendering screenshots with the GPU API if supported by the backend
   (instead of using a suboptimal software fallback via libswscale).

Warning: do not just add this without reading the "Threading" section
         above, and then wondering that deadlocks happen. The
         requirements are tricky. But also note that even if advanced
         control is disabled, not adhering to the rules will lead to
         playback problems. Enabling advanced controls simply makes
         violating these rules fatal.

Type: int*: 0 for disable (default), 1 for enable*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_ADVANCED_CONTROL: mpv_render_param_type = 10;
/** Return information about the next frame to render. Valid for
mpv_render_context_get_info().

Type: mpv_render_frame_info*

It strictly returns information about the _next_ frame. The implication
is that e.g. mpv_render_context_update()'s return value will have
MPV_RENDER_UPDATE_FRAME set, and the user is supposed to call
mpv_render_context_render(). If there is no next frame, then the
return value will have is_valid set to 0.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_NEXT_FRAME_INFO: mpv_render_param_type = 11;
/** Enable or disable video timing. Valid for mpv_render_context_render().

Type: int*: 0 for disable, 1 for enable (default)

When video is timed to audio, the player attempts to render video a bit
ahead, and then do a blocking wait until the target display time is
reached. This blocks mpv_render_context_render() for up to the amount
specified with the "video-timing-offset" global option. You can set
this parameter to 0 to disable this kind of waiting. If you do, it's
recommended to use the target time value in mpv_render_frame_info to
wait yourself, or to set the "video-timing-offset" to 0 instead.

Disabling this without doing anything in addition will result in A/V sync
being slightly off.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_BLOCK_FOR_TARGET_TIME: mpv_render_param_type = 12;
/** Use to skip rendering in mpv_render_context_render().

Type: int*: 0 for rendering (default), 1 for skipping

If this is set, you don't need to pass a target surface to the render
function (and if you do, it's completely ignored). This can still call
into the lower level APIs (i.e. if you use OpenGL, the OpenGL context
must be set).

Be aware that the render API will consider this frame as having been
rendered. All other normal rules also apply, for example about whether
you have to call mpv_render_context_report_swap(). It also does timing
in the same way.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_SKIP_RENDERING: mpv_render_param_type = 13;
/** Deprecated. Not supported. Use MPV_RENDER_PARAM_DRM_DISPLAY_V2 instead.
Type : struct mpv_opengl_drm_params**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_DRM_DISPLAY: mpv_render_param_type = 14;
/** DRM draw surface size, contains draw surface dimensions.
Valid for mpv_render_context_create().
Type : struct mpv_opengl_drm_draw_surface_size**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_DRM_DRAW_SURFACE_SIZE: mpv_render_param_type = 15;
/** DRM display, contains drm display handles.
Valid for mpv_render_context_create().
Type : struct mpv_opengl_drm_params_v2**/
pub const mpv_render_param_type_MPV_RENDER_PARAM_DRM_DISPLAY_V2: mpv_render_param_type = 16;
/** MPV_RENDER_API_TYPE_SW only: rendering target surface size, mandatory.
Valid for MPV_RENDER_API_TYPE_SW & mpv_render_context_render().
Type: int[2] (e.g.: int s[2] = {w, h}; param.data = &s[0];)

The video frame is transformed as with other VOs. Typically, this means
the video gets scaled and black bars are added if the video size or
aspect ratio mismatches with the target size.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_SW_SIZE: mpv_render_param_type = 17;
/** MPV_RENDER_API_TYPE_SW only: rendering target surface pixel format,
mandatory.
Valid for MPV_RENDER_API_TYPE_SW & mpv_render_context_render().
Type: char* (e.g.: char *f = "rgb0"; param.data = f;)

Valid values are:
 "rgb0", "bgr0", "0bgr", "0rgb"
     4 bytes per pixel RGB, 1 byte (8 bit) per component, component bytes
     with increasing address from left to right (e.g. "rgb0" has r at
     address 0), the "0" component contains uninitialized garbage (often
     the value 0, but not necessarily; the bad naming is inherited from
     FFmpeg)
     Pixel alignment size: 4 bytes
 "rgb24"
     3 bytes per pixel RGB. This is strongly discouraged because it is
     very slow.
     Pixel alignment size: 1 bytes
 other
     The API may accept other pixel formats, using mpv internal format
     names, as long as it's internally marked as RGB, has exactly 1
     plane, and is supported as conversion output. It is not a good idea
     to rely on any of these. Their semantics and handling could change.*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_SW_FORMAT: mpv_render_param_type = 18;
/** MPV_RENDER_API_TYPE_SW only: rendering target surface bytes per line,
mandatory.
Valid for MPV_RENDER_API_TYPE_SW & mpv_render_context_render().
Type: size_t*

This is the number of bytes between a pixel (x, y) and (x, y + 1) on the
target surface. It must be a multiple of the pixel size, and have space
for the surface width as specified by MPV_RENDER_PARAM_SW_SIZE.

Both stride and pointer value should be a multiple of 64 to facilitate
fast SIMD operation. Lower alignment might trigger slower code paths,
and in the worst case, will copy the entire target frame. If mpv is built
with zimg (and zimg is not disabled), the performance impact might be
less.
In either cases, the pointer and stride must be aligned at least to the
pixel alignment size. Otherwise, crashes and undefined behavior is
possible on platforms which do not support unaligned accesses (either
through normal memory access or aligned SIMD memory access instructions).*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_SW_STRIDE: mpv_render_param_type = 19;
/** MPV_RENDER_API_TYPE_SW only: rendering target surface bytes per line,
mandatory.
Valid for MPV_RENDER_API_TYPE_SW & mpv_render_context_render().
Type: size_t*

This is the number of bytes between a pixel (x, y) and (x, y + 1) on the
target surface. It must be a multiple of the pixel size, and have space
for the surface width as specified by MPV_RENDER_PARAM_SW_SIZE.

Both stride and pointer value should be a multiple of 64 to facilitate
fast SIMD operation. Lower alignment might trigger slower code paths,
and in the worst case, will copy the entire target frame. If mpv is built
with zimg (and zimg is not disabled), the performance impact might be
less.
In either cases, the pointer and stride must be aligned at least to the
pixel alignment size. Otherwise, crashes and undefined behavior is
possible on platforms which do not support unaligned accesses (either
through normal memory access or aligned SIMD memory access instructions).*/
pub const mpv_render_param_type_MPV_RENDER_PARAM_SW_POINTER: mpv_render_param_type = 20;
/** Parameters for mpv_render_param (which is used in a few places such as
mpv_render_context_create().

Also see mpv_render_param for conventions and how to use it.*/
pub type mpv_render_param_type = ::std::os::raw::c_uint;
/** Used to pass arbitrary parameters to some mpv_render_* functions. The
meaning of the data parameter is determined by the type, and each
MPV_RENDER_PARAM_* documents what type the value must point to.

Each value documents the required data type as the pointer you cast to
void* and set on mpv_render_param.data. For example, if MPV_RENDER_PARAM_FOO
documents the type as Something* , then the code should look like this:

  Something foo = {...};
  mpv_render_param param;
  param.type = MPV_RENDER_PARAM_FOO;
  param.data = & foo;

Normally, the data field points to exactly 1 object. If the type is char*,
it points to a 0-terminated string.

In all cases (unless documented otherwise) the pointers need to remain
valid during the call only. Unless otherwise documented, the API functions
will not write to the params array or any data pointed to it.

As a convention, parameter arrays are always terminated by type==0. There
is no specific order of the parameters required. The order of the 2 fields in
this struct is guaranteed (even after ABI changes).*/
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_render_param {
    pub type_: mpv_render_param_type,
    pub data: *mut ::std::os::raw::c_void,
}
#[test]
fn bindgen_test_layout_mpv_render_param() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_render_param> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_render_param>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_render_param)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_render_param>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_render_param)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).type_) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_render_param),
            "::",
            stringify!(type_),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).data) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_render_param),
            "::",
            stringify!(data),
        ),
    );
}
/** Set if there is actually a next frame. If unset, there is no next frame
yet, and other flags and fields that require a frame to be queued will
be unset.

This is set for _any_ kind of frame, even for redraw requests.

Note that when this is unset, it simply means no new frame was
decoded/queued yet, not necessarily that the end of the video was
reached. A new frame can be queued after some time.

If the return value of mpv_render_context_render() had the
MPV_RENDER_UPDATE_FRAME flag set, this flag will usually be set as well,
unless the frame is rendered, or discarded by other asynchronous events.*/
pub const mpv_render_frame_info_flag_MPV_RENDER_FRAME_INFO_PRESENT: mpv_render_frame_info_flag = 1;
/** If set, the frame is not an actual new video frame, but a redraw request.
For example if the video is paused, and an option that affects video
rendering was changed (or any other reason), an update request can be
issued and this flag will be set.

Typically, redraw frames will not be subject to video timing.

Implies MPV_RENDER_FRAME_INFO_PRESENT.*/
pub const mpv_render_frame_info_flag_MPV_RENDER_FRAME_INFO_REDRAW: mpv_render_frame_info_flag = 2;
/** If set, this is supposed to reproduce the previous frame perfectly. This
is usually used for certain "video-sync" options ("display-..." modes).
Typically the renderer will blit the video from a FBO. Unset otherwise.

Implies MPV_RENDER_FRAME_INFO_PRESENT.*/
pub const mpv_render_frame_info_flag_MPV_RENDER_FRAME_INFO_REPEAT: mpv_render_frame_info_flag = 4;
/** If set, the player timing code expects that the user thread blocks on
vsync (by either delaying the render call, or by making a call to
mpv_render_context_report_swap() at vsync time).

Implies MPV_RENDER_FRAME_INFO_PRESENT.*/
pub const mpv_render_frame_info_flag_MPV_RENDER_FRAME_INFO_BLOCK_VSYNC: mpv_render_frame_info_flag =
    8;
/// Flags used in mpv_render_frame_info.flags. Each value represents a bit in it.
pub type mpv_render_frame_info_flag = ::std::os::raw::c_uint;
/** Information about the next video frame that will be rendered. Can be
retrieved with MPV_RENDER_PARAM_NEXT_FRAME_INFO.*/
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_render_frame_info {
    /** A bitset of mpv_render_frame_info_flag values (i.e. multiple flags are
    combined with bitwise or).*/
    pub flags: u64,
    /** Absolute time at which the frame is supposed to be displayed. This is in
    the same unit and base as the time returned by mpv_get_time_us(). For
    frames that are redrawn, or if vsync locked video timing is used (see
    "video-sync" option), then this can be 0. The "video-timing-offset"
    option determines how much "headroom" the render thread gets (but a high
    enough frame rate can reduce it anyway). mpv_render_context_render() will
    normally block until the time is elapsed, unless you pass it
    MPV_RENDER_PARAM_BLOCK_FOR_TARGET_TIME = 0.*/
    pub target_time: i64,
}
#[test]
fn bindgen_test_layout_mpv_render_frame_info() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_render_frame_info> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_render_frame_info>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_render_frame_info)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_render_frame_info>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_render_frame_info)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).flags) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_render_frame_info),
            "::",
            stringify!(flags),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).target_time) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_render_frame_info),
            "::",
            stringify!(target_time),
        ),
    );
}
extern "C" {
    /** Initialize the renderer state. Depending on the backend used, this will
    access the underlying GPU API and initialize its own objects.

    You must free the context with mpv_render_context_free(). Not doing so before
    the mpv core is destroyed may result in memory leaks or crashes.

    Currently, only at most 1 context can exists per mpv core (it represents the
    main video output).

    You should pass the following parameters:
     - MPV_RENDER_PARAM_API_TYPE to select the underlying backend/GPU API.
     - Backend-specific init parameter, like MPV_RENDER_PARAM_OPENGL_INIT_PARAMS.
     - Setting MPV_RENDER_PARAM_ADVANCED_CONTROL and following its rules is
       strongly recommended.
     - If you want to use hwdec, possibly hwdec interop resources.

    @param res set to the context (on success) or NULL (on failure). The value
               is never read and always overwritten.
    @param mpv handle used to get the core (the mpv_render_context won't depend
               on this specific handle, only the core referenced by it)
    @param params an array of parameters, terminated by type==0. It's left
                  unspecified what happens with unknown parameters. At least
                  MPV_RENDER_PARAM_API_TYPE is required, and most backends will
                  require another backend-specific parameter.
    @return error code, including but not limited to:
         MPV_ERROR_UNSUPPORTED: the OpenGL version is not supported
                                (or required extensions are missing)
         MPV_ERROR_NOT_IMPLEMENTED: an unknown API type was provided, or
                                    support for the requested API was not
                                    built in the used libmpv binary.
         MPV_ERROR_INVALID_PARAMETER: at least one of the provided parameters was
                                      not valid.*/
    pub fn mpv_render_context_create(
        res: *mut *mut mpv_render_context,
        mpv: *mut mpv_handle,
        params: *mut mpv_render_param,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Attempt to change a single parameter. Not all backends and parameter types
    support all kinds of changes.

    @param ctx a valid render context
    @param param the parameter type and data that should be set
    @return error code. If a parameter could actually be changed, this returns
            success, otherwise an error code depending on the parameter type
            and situation.*/
    pub fn mpv_render_context_set_parameter(
        ctx: *mut mpv_render_context,
        param: mpv_render_param,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Retrieve information from the render context. This is NOT a counterpart to
    mpv_render_context_set_parameter(), because you generally can't read
    parameters set with it, and this function is not meant for this purpose.
    Instead, this is for communicating information from the renderer back to the
    user. See mpv_render_param_type; entries which support this function
    explicitly mention it, and for other entries you can assume it will fail.

    You pass param with param.type set and param.data pointing to a variable
    of the required data type. The function will then overwrite that variable
    with the returned value (at least on success).

    @param ctx a valid render context
    @param param the parameter type and data that should be retrieved
    @return error code. If a parameter could actually be retrieved, this returns
            success, otherwise an error code depending on the parameter type
            and situation. MPV_ERROR_NOT_IMPLEMENTED is used for unknown
            param.type, or if retrieving it is not supported.*/
    pub fn mpv_render_context_get_info(
        ctx: *mut mpv_render_context,
        param: mpv_render_param,
    ) -> ::std::os::raw::c_int;
}
pub type mpv_render_update_fn =
    ::std::option::Option<unsafe extern "C" fn(cb_ctx: *mut ::std::os::raw::c_void)>;
extern "C" {
    /** Set the callback that notifies you when a new video frame is available, or
    if the video display configuration somehow changed and requires a redraw.
    Similar to mpv_set_wakeup_callback(), you must not call any mpv API from
    the callback, and all the other listed restrictions apply (such as not
    exiting the callback by throwing exceptions).

    This can be called from any thread, except from an update callback. In case
    of the OpenGL backend, no OpenGL state or API is accessed.

    Calling this will raise an update callback immediately.

    @param callback callback(callback_ctx) is called if the frame should be
                    redrawn
    @param callback_ctx opaque argument to the callback*/
    pub fn mpv_render_context_set_update_callback(
        ctx: *mut mpv_render_context,
        callback: mpv_render_update_fn,
        callback_ctx: *mut ::std::os::raw::c_void,
    );
}
extern "C" {
    /** The API user is supposed to call this when the update callback was invoked
    (like all mpv_render_* functions, this has to happen on the render thread,
    and _not_ from the update callback itself).

    This is optional if MPV_RENDER_PARAM_ADVANCED_CONTROL was not set (default).
    Otherwise, it's a hard requirement that this is called after each update
    callback. If multiple update callback happened, and the function could not
    be called sooner, it's OK to call it once after the last callback.

    If an update callback happens during or after this function, the function
    must be called again at the soonest possible time.

    If MPV_RENDER_PARAM_ADVANCED_CONTROL was set, this will do additional work
    such as allocating textures for the video decoder.

    @return a bitset of mpv_render_update_flag values (i.e. multiple flags are
            combined with bitwise or). Typically, this will tell the API user
            what should happen next. E.g. if the MPV_RENDER_UPDATE_FRAME flag is
            set, mpv_render_context_render() should be called. If flags unknown
            to the API user are set, or if the return value is 0, nothing needs
            to be done.*/
    pub fn mpv_render_context_update(ctx: *mut mpv_render_context) -> u64;
}
/** A new video frame must be rendered. mpv_render_context_render() must be
called.*/
pub const mpv_render_update_flag_MPV_RENDER_UPDATE_FRAME: mpv_render_update_flag = 1;
/** Flags returned by mpv_render_context_update(). Each value represents a bit
in the function's return value.*/
pub type mpv_render_update_flag = ::std::os::raw::c_uint;
/** Flags returned by mpv_render_context_update(). Each value represents a bit
in the function's return value.*/
pub use self::mpv_render_update_flag as mpv_render_context_flag;
extern "C" {
    /** Render video.

    Typically renders the video to a target surface provided via mpv_render_param
    (the details depend on the backend in use). Options like "panscan" are
    applied to determine which part of the video should be visible and how the
    video should be scaled. You can change these options at runtime by using the
    mpv property API.

    The renderer will reconfigure itself every time the target surface
    configuration (such as size) is changed.

    This function implicitly pulls a video frame from the internal queue and
    renders it. If no new frame is available, the previous frame is redrawn.
    The update callback set with mpv_render_context_set_update_callback()
    notifies you when a new frame was added. The details potentially depend on
    the backends and the provided parameters.

    Generally, libmpv will invoke your update callback some time before the video
    frame should be shown, and then lets this function block until the supposed
    display time. This will limit your rendering to video FPS. You can prevent
    this by setting the "video-timing-offset" global option to 0. (This applies
    only to "audio" video sync mode.)

    You should pass the following parameters:
     - Backend-specific target object, such as MPV_RENDER_PARAM_OPENGL_FBO.
     - Possibly transformations, such as MPV_RENDER_PARAM_FLIP_Y.

    @param ctx a valid render context
    @param params an array of parameters, terminated by type==0. Which parameters
                  are required depends on the backend. It's left unspecified what
                  happens with unknown parameters.
    @return error code*/
    pub fn mpv_render_context_render(
        ctx: *mut mpv_render_context,
        params: *mut mpv_render_param,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    /** Tell the renderer that a frame was flipped at the given time. This is
    optional, but can help the player to achieve better timing.

    Note that calling this at least once informs libmpv that you will use this
    function. If you use it inconsistently, expect bad video playback.

    If this is called while no video is initialized, it is ignored.

    @param ctx a valid render context*/
    pub fn mpv_render_context_report_swap(ctx: *mut mpv_render_context);
}
extern "C" {
    /** Destroy the mpv renderer state.

    If video is still active (e.g. a file playing), video will be disabled
    forcefully.

    @param ctx a valid render context. After this function returns, this is not
               a valid pointer anymore. NULL is also allowed and does nothing.*/
    pub fn mpv_render_context_free(ctx: *mut mpv_render_context);
}
/// For initializing the mpv OpenGL state via MPV_RENDER_PARAM_OPENGL_INIT_PARAMS.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_opengl_init_params {
    /** This retrieves OpenGL function pointers, and will use them in subsequent
    operation.
    Usually, you can simply call the GL context APIs from this callback (e.g.
    glXGetProcAddressARB or wglGetProcAddress), but some APIs do not always
    return pointers for all standard functions (even if present); in this
    case you have to compensate by looking up these functions yourself when
    libmpv wants to resolve them through this callback.
    libmpv will not normally attempt to resolve GL functions on its own, nor
    does it link to GL libraries directly.*/
    pub get_proc_address: ::std::option::Option<
        unsafe extern "C" fn(
            ctx: *mut ::std::os::raw::c_void,
            name: *const ::std::os::raw::c_char,
        ) -> *mut ::std::os::raw::c_void,
    >,
    /// Value passed as ctx parameter to get_proc_address().
    pub get_proc_address_ctx: *mut ::std::os::raw::c_void,
}
#[test]
fn bindgen_test_layout_mpv_opengl_init_params() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_opengl_init_params> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_opengl_init_params>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_opengl_init_params)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_opengl_init_params>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_opengl_init_params)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).get_proc_address) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_init_params),
            "::",
            stringify!(get_proc_address),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).get_proc_address_ctx) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_init_params),
            "::",
            stringify!(get_proc_address_ctx),
        ),
    );
}
/// For MPV_RENDER_PARAM_OPENGL_FBO.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_opengl_fbo {
    /** Framebuffer object name. This must be either a valid FBO generated by
    glGenFramebuffers() that is complete and color-renderable, or 0. If the
    value is 0, this refers to the OpenGL default framebuffer.*/
    pub fbo: ::std::os::raw::c_int,
    /** Valid dimensions. This must refer to the size of the framebuffer. This
    must always be set.*/
    pub w: ::std::os::raw::c_int,
    /** Valid dimensions. This must refer to the size of the framebuffer. This
    must always be set.*/
    pub h: ::std::os::raw::c_int,
    /** Underlying texture internal format (e.g. GL_RGBA8), or 0 if unknown. If
    this is the default framebuffer, this can be an equivalent.*/
    pub internal_format: ::std::os::raw::c_int,
}
#[test]
fn bindgen_test_layout_mpv_opengl_fbo() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_opengl_fbo> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_opengl_fbo>(),
        16usize,
        concat!("Size of: ", stringify!(mpv_opengl_fbo)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_opengl_fbo>(),
        4usize,
        concat!("Alignment of ", stringify!(mpv_opengl_fbo)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).fbo) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_fbo),
            "::",
            stringify!(fbo)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).w) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_fbo),
            "::",
            stringify!(w)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).h) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_fbo),
            "::",
            stringify!(h)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).internal_format) as usize - ptr as usize },
        12usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_fbo),
            "::",
            stringify!(internal_format),
        ),
    );
}
/// Deprecated. For MPV_RENDER_PARAM_DRM_DISPLAY.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_opengl_drm_params {
    pub fd: ::std::os::raw::c_int,
    pub crtc_id: ::std::os::raw::c_int,
    pub connector_id: ::std::os::raw::c_int,
    pub atomic_request_ptr: *mut *mut _drmModeAtomicReq,
    pub render_fd: ::std::os::raw::c_int,
}
#[test]
fn bindgen_test_layout_mpv_opengl_drm_params() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_opengl_drm_params> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_opengl_drm_params>(),
        32usize,
        concat!("Size of: ", stringify!(mpv_opengl_drm_params)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_opengl_drm_params>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_opengl_drm_params)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).fd) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params),
            "::",
            stringify!(fd),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).crtc_id) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params),
            "::",
            stringify!(crtc_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).connector_id) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params),
            "::",
            stringify!(connector_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).atomic_request_ptr) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params),
            "::",
            stringify!(atomic_request_ptr),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).render_fd) as usize - ptr as usize },
        24usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params),
            "::",
            stringify!(render_fd),
        ),
    );
}
/// For MPV_RENDER_PARAM_DRM_DRAW_SURFACE_SIZE.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_opengl_drm_draw_surface_size {
    /// size of the draw plane surface in pixels.
    pub width: ::std::os::raw::c_int,
    /// size of the draw plane surface in pixels.
    pub height: ::std::os::raw::c_int,
}
#[test]
fn bindgen_test_layout_mpv_opengl_drm_draw_surface_size() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_opengl_drm_draw_surface_size> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_opengl_drm_draw_surface_size>(),
        8usize,
        concat!("Size of: ", stringify!(mpv_opengl_drm_draw_surface_size)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_opengl_drm_draw_surface_size>(),
        4usize,
        concat!(
            "Alignment of ",
            stringify!(mpv_opengl_drm_draw_surface_size)
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).width) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_draw_surface_size),
            "::",
            stringify!(width),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).height) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_draw_surface_size),
            "::",
            stringify!(height),
        ),
    );
}
/// For MPV_RENDER_PARAM_DRM_DISPLAY_V2.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_opengl_drm_params_v2 {
    /// DRM fd (int). Set to -1 if invalid.
    pub fd: ::std::os::raw::c_int,
    /// Currently used crtc id
    pub crtc_id: ::std::os::raw::c_int,
    /// Currently used connector id
    pub connector_id: ::std::os::raw::c_int,
    /** Pointer to a drmModeAtomicReq pointer that is being used for the renderloop.
    This pointer should hold a pointer to the atomic request pointer
    The atomic request pointer is usually changed at every renderloop.*/
    pub atomic_request_ptr: *mut *mut _drmModeAtomicReq,
    /** DRM render node. Used for VAAPI interop.
    Set to -1 if invalid.*/
    pub render_fd: ::std::os::raw::c_int,
}
#[test]
fn bindgen_test_layout_mpv_opengl_drm_params_v2() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_opengl_drm_params_v2> =
        ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_opengl_drm_params_v2>(),
        32usize,
        concat!("Size of: ", stringify!(mpv_opengl_drm_params_v2)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_opengl_drm_params_v2>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_opengl_drm_params_v2)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).fd) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params_v2),
            "::",
            stringify!(fd),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).crtc_id) as usize - ptr as usize },
        4usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params_v2),
            "::",
            stringify!(crtc_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).connector_id) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params_v2),
            "::",
            stringify!(connector_id),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).atomic_request_ptr) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params_v2),
            "::",
            stringify!(atomic_request_ptr),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).render_fd) as usize - ptr as usize },
        24usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_opengl_drm_params_v2),
            "::",
            stringify!(render_fd),
        ),
    );
}
/** Read callback used to implement a custom stream. The semantics of the
callback match read(2) in blocking mode. Short reads are allowed (you can
return less bytes than requested, and libmpv will retry reading the rest
with another call). If no data can be immediately read, the callback must
block until there is new data. A return of 0 will be interpreted as final
EOF, although libmpv might retry the read, or seek to a different position.

@param cookie opaque cookie identifying the stream,
              returned from mpv_stream_cb_open_fn
@param buf buffer to read data into
@param size of the buffer
@return number of bytes read into the buffer
@return 0 on EOF
@return -1 on error*/
pub type mpv_stream_cb_read_fn = ::std::option::Option<
    unsafe extern "C" fn(
        cookie: *mut ::std::os::raw::c_void,
        buf: *mut ::std::os::raw::c_char,
        nbytes: u64,
    ) -> i64,
>;
/** Seek callback used to implement a custom stream.

Note that mpv will issue a seek to position 0 immediately after opening. This
is used to test whether the stream is seekable (since seekability might
depend on the URI contents, not just the protocol). Return
MPV_ERROR_UNSUPPORTED if seeking is not implemented for this stream. This
seek also serves to establish the fact that streams start at position 0.

This callback can be NULL, in which it behaves as if always returning
MPV_ERROR_UNSUPPORTED.

@param cookie opaque cookie identifying the stream,
              returned from mpv_stream_cb_open_fn
@param offset target absolute stream position
@return the resulting offset of the stream
        MPV_ERROR_UNSUPPORTED or MPV_ERROR_GENERIC if the seek failed*/
pub type mpv_stream_cb_seek_fn = ::std::option::Option<
    unsafe extern "C" fn(cookie: *mut ::std::os::raw::c_void, offset: i64) -> i64,
>;
/** Size callback used to implement a custom stream.

Return MPV_ERROR_UNSUPPORTED if no size is known.

This callback can be NULL, in which it behaves as if always returning
MPV_ERROR_UNSUPPORTED.

@param cookie opaque cookie identifying the stream,
              returned from mpv_stream_cb_open_fn
@return the total size in bytes of the stream*/
pub type mpv_stream_cb_size_fn =
    ::std::option::Option<unsafe extern "C" fn(cookie: *mut ::std::os::raw::c_void) -> i64>;
/** Close callback used to implement a custom stream.

@param cookie opaque cookie identifying the stream,
              returned from mpv_stream_cb_open_fn*/
pub type mpv_stream_cb_close_fn =
    ::std::option::Option<unsafe extern "C" fn(cookie: *mut ::std::os::raw::c_void)>;
/** Cancel callback used to implement a custom stream.

This callback is used to interrupt any current or future read and seek
operations. It will be called from a separate thread than the demux
thread, and should not block.

This callback can be NULL.

Available since API 1.106.

@param cookie opaque cookie identifying the stream,
              returned from mpv_stream_cb_open_fn*/
pub type mpv_stream_cb_cancel_fn =
    ::std::option::Option<unsafe extern "C" fn(cookie: *mut ::std::os::raw::c_void)>;
/// See mpv_stream_cb_open_ro_fn callback.
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mpv_stream_cb_info {
    /** Opaque user-provided value, which will be passed to the other callbacks.
    The close callback will be called to release the cookie. It is not
    interpreted by mpv. It doesn't even need to be a valid pointer.

    The user sets this in the mpv_stream_cb_open_ro_fn callback.*/
    pub cookie: *mut ::std::os::raw::c_void,
    /** Callbacks set by the user in the mpv_stream_cb_open_ro_fn callback. Some
    of them are optional, and can be left unset.

    The following callbacks are mandatory: read_fn, close_fn*/
    pub read_fn: mpv_stream_cb_read_fn,
    pub seek_fn: mpv_stream_cb_seek_fn,
    pub size_fn: mpv_stream_cb_size_fn,
    pub close_fn: mpv_stream_cb_close_fn,
    pub cancel_fn: mpv_stream_cb_cancel_fn,
}
#[test]
fn bindgen_test_layout_mpv_stream_cb_info() {
    const UNINIT: ::std::mem::MaybeUninit<mpv_stream_cb_info> = ::std::mem::MaybeUninit::uninit();
    let ptr = UNINIT.as_ptr();
    assert_eq!(
        ::std::mem::size_of::<mpv_stream_cb_info>(),
        48usize,
        concat!("Size of: ", stringify!(mpv_stream_cb_info)),
    );
    assert_eq!(
        ::std::mem::align_of::<mpv_stream_cb_info>(),
        8usize,
        concat!("Alignment of ", stringify!(mpv_stream_cb_info)),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).cookie) as usize - ptr as usize },
        0usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(cookie),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).read_fn) as usize - ptr as usize },
        8usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(read_fn),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).seek_fn) as usize - ptr as usize },
        16usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(seek_fn),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).size_fn) as usize - ptr as usize },
        24usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(size_fn),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).close_fn) as usize - ptr as usize },
        32usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(close_fn),
        ),
    );
    assert_eq!(
        unsafe { ::std::ptr::addr_of!((*ptr).cancel_fn) as usize - ptr as usize },
        40usize,
        concat!(
            "Offset of field: ",
            stringify!(mpv_stream_cb_info),
            "::",
            stringify!(cancel_fn),
        ),
    );
}
/** Open callback used to implement a custom read-only (ro) stream. The user
must set the callback fields in the passed info struct. The cookie field
also can be set to store state associated to the stream instance.

Note that the info struct is valid only for the duration of this callback.
You can't change the callbacks or the pointer to the cookie at a later point.

Each stream instance created by the open callback can have different
callbacks.

The close_fn callback will terminate the stream instance. The pointers to
your callbacks and cookie will be discarded, and the callbacks will not be
called again.

@param user_data opaque user data provided via mpv_stream_cb_add()
@param uri name of the stream to be opened (with protocol prefix)
@param info fields which the user should fill
@return 0 on success, MPV_ERROR_LOADING_FAILED if the URI cannot be opened.*/
pub type mpv_stream_cb_open_ro_fn = ::std::option::Option<
    unsafe extern "C" fn(
        user_data: *mut ::std::os::raw::c_void,
        uri: *mut ::std::os::raw::c_char,
        info: *mut mpv_stream_cb_info,
    ) -> ::std::os::raw::c_int,
>;
extern "C" {
    /** Add a custom stream protocol. This will register a protocol handler under
    the given protocol prefix, and invoke the given callbacks if an URI with the
    matching protocol prefix is opened.

    The "ro" is for read-only - only read-only streams can be registered with
    this function.

    The callback remains registered until the mpv core is registered.

    If a custom stream with the same name is already registered, then the
    MPV_ERROR_INVALID_PARAMETER error is returned.

    @param protocol protocol prefix, for example "foo" for "foo://" URIs
    @param user_data opaque pointer passed into the mpv_stream_cb_open_fn
                     callback.
    @return error code*/
    pub fn mpv_stream_cb_add_ro(
        ctx: *mut mpv_handle,
        protocol: *const ::std::os::raw::c_char,
        user_data: *mut ::std::os::raw::c_void,
        open_fn: mpv_stream_cb_open_ro_fn,
    ) -> ::std::os::raw::c_int;
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _drmModeAtomicReq {
    pub _address: u8,
}

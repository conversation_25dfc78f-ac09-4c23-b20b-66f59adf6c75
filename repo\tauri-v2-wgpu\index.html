<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="/src/styles.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tauri App</title>
    <script type="module" src="/src/main.ts" defer></script>
    <style>
      .logo.vite:hover {
        filter: drop-shadow(0 0 2em #747bff);
      }

      .logo.typescript:hover {
        filter: drop-shadow(0 0 2em #2d79c7);
      }
    </style>
  </head>

  <body>
    <div class="container">
      <h1>Welcome to Tauri!</h1>

      <div class="row">
        <a href="https://vitejs.dev" target="_blank">
          <img src="/src/assets/vite.svg" class="logo vite" alt="Vite logo" />
        </a>
        <a href="https://tauri.app" target="_blank">
          <img
            src="/src/assets/tauri.svg"
            class="logo tauri"
            alt="Tauri logo"
          />
        </a>
        <a href="https://www.typescriptlang.org/docs" target="_blank">
          <img
            src="/src/assets/typescript.svg"
            class="logo typescript"
            alt="typescript logo"
          />
        </a>
      </div>

      <p>Click on the Tauri logo to learn more about the framework</p>

      <form class="row" id="greet-form">
        <input id="greet-input" placeholder="Enter a name..." />
        <button type="submit">Greet</button>
      </form>

      <p id="greet-msg"></p>
    </div>
  </body>
</html>

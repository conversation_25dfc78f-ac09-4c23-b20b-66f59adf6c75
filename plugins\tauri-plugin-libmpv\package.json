{"name": "tauri-plugin-libmpv-api", "version": "0.0.2", "author": "22", "description": "A Tauri plugin for embedding the libmpv player in your app via libmpv.", "license": "LGPL-2.1", "type": "module", "homepage": "https://github.com/nini22P/mpv-tauri/tree/main/plugins/tauri-plugin-libmpv", "repository": {"type": "git", "url": "https://github.com/nini22P/mpv-tauri.git"}, "bugs": {"url": "https://github.com/nini22P/mpv-tauri/issues"}, "keywords": ["tauri", "plugin", "mpv", "media", "player", "video", "audio", "rust", "typescript"], "engines": {"node": ">=18.0.0"}, "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md", "LICENSE"], "scripts": {"build": "rollup -c", "prepublishOnly": "pnpm run build", "pretest": "pnpm run build"}, "dependencies": {"@tauri-apps/api": "2.8.0"}, "devDependencies": {"@rollup/plugin-typescript": "12.1.4", "rollup": "^4.50.1", "tslib": "^2.6.2", "typescript": "^5.3.3"}}
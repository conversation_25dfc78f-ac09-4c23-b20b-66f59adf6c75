## Default Permission

Default permissions for the plugin

#### This default permission set includes the following:

- `allow-init`
- `allow-destroy`
- `allow-command`
- `allow-set-property`
- `allow-get-property`
- `allow-set-video-margin-ratio`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`libmpv:allow-command`

</td>
<td>

Enables the command command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-command`

</td>
<td>

Denies the command command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:allow-destroy`

</td>
<td>

Enables the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-destroy`

</td>
<td>

Denies the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:allow-get-property`

</td>
<td>

Enables the get_property command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-get-property`

</td>
<td>

Denies the get_property command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:allow-init`

</td>
<td>

Enables the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-init`

</td>
<td>

Denies the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:allow-set-property`

</td>
<td>

Enables the set_property command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-set-property`

</td>
<td>

Denies the set_property command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:allow-set-video-margin-ratio`

</td>
<td>

Enables the set_video_margin_ratio command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`libmpv:deny-set-video-margin-ratio`

</td>
<td>

Denies the set_video_margin_ratio command without any pre-configured scope.

</td>
</tr>
</table>

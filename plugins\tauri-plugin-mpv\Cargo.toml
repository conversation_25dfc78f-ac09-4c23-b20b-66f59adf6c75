[package]
name = "tauri-plugin-mpv"
version = "0.4.0"
authors = ["22"]
description = "A Tauri plugin for embedding the mpv player in your app via JSON IPC."
edition = "2021"
rust-version = "1.77.2"
license = "LGPL-2.1"
repository = "https://github.com/nini22P/mpv-tauri"
keywords = ["tauri", "plugin", "mpv", "media", "player"]
categories = ["multimedia", "gui", "api-bindings"]
readme = "README.md"
exclude = ["/examples", "/dist-js", "/guest-js", "/node_modules", "/target"]
links = "tauri-plugin-mpv"

[dependencies]
tauri = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
raw-window-handle = { workspace = true }
log = { workspace = true }

[target.'cfg(windows)'.dependencies]

[target.'cfg(unix)'.dependencies]

[build-dependencies]
tauri-plugin = { version = "2", features = ["build"] }

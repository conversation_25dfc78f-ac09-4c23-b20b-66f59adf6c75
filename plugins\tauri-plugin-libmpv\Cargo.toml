[package]
name = "tauri-plugin-libmpv"
version = "0.0.2"
authors = ["22"]
description = "A Tauri plugin for embedding the mpv player in your app via libmpv."
edition = "2021"
rust-version = "1.77.2"
license = "LGPL-2.1"
repository = "https://github.com/nini22P/mpv-tauri"
keywords = ["tauri", "plugin", "mpv", "media", "player"]
categories = ["multimedia", "gui", "api-bindings"]
readme = "README.md"
exclude = ["/examples", "/dist-js", "/guest-js", "/node_modules", "/target"]
links = "tauri-plugin-libmpv"

[dependencies]
tauri = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
raw-window-handle = { workspace = true }
log = { workspace = true }
libmpv2 = "5.0.1"
libmpv2-sys = "4.0.0"
tauri-plugin-egui = { git = "https://github.com/clearlysid/tauri-plugin-egui" }

[target.'cfg(windows)'.dependencies]

[target.'cfg(unix)'.dependencies]

[build-dependencies]
tauri-plugin = { version = "2", features = ["build"] }

# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-property"
description = "Enables the get_property command without any pre-configured scope."
commands.allow = ["get_property"]

[[permission]]
identifier = "deny-get-property"
description = "Denies the get_property command without any pre-configured scope."
commands.deny = ["get_property"]

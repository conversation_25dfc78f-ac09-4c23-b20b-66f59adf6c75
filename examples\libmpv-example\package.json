{"name": "tauri-plugin-libmpv-example", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-cli": "^2.4.0", "@tauri-apps/plugin-dialog": "^2.4.0", "@tauri-apps/plugin-log": "^2.7.0", "@tauri-apps/plugin-opener": "^2.5.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tauri-plugin-libmpv-api": "workspace:*", "zustand": "^5.0.8"}, "devDependencies": {"@tauri-apps/cli": "^2.8.4", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "typescript": "^5.9.2", "vite": "^7.1.5"}}
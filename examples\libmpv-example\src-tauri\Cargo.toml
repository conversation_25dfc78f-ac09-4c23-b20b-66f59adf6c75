[package]
name = "tauri_plugin_libmpv_example"
version = "0.1.0"
description = "A example program for embedding mpv player in Tauri via libmpv."
authors = ["22"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_plugin_libmpv_example_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.4.1", features = [] }

[dependencies]
tauri = { version = "2.8.5", features = [] }
tauri-plugin-opener = "2.5.0"
tauri-plugin-libmpv = { path = "../../../plugins/tauri-plugin-libmpv" }
serde_json = "1.0.143"
tauri-plugin-dialog = "2.4.0"
tauri-plugin-log = "2.7.0"

[target.'cfg(windows)'.dependencies]

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-cli = "2.4.0"

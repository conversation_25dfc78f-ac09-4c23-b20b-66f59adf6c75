# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-set-property"
description = "Enables the set_property command without any pre-configured scope."
commands.allow = ["set_property"]

[[permission]]
identifier = "deny-set-property"
description = "Denies the set_property command without any pre-configured scope."
commands.deny = ["set_property"]

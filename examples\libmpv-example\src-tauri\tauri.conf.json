{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-plugin-libmpv-example", "mainBinaryName": "tauri-plugin-libmpv-example", "version": "0.1.0", "identifier": "nini22p.tauri-plugin-libmpv-example", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "tauri-plugin-libmpv-example", "width": 1280, "height": 720, "transparent": true, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"cli": {"description": "tauri-plugin-libmpv-example", "args": [{"name": "source", "index": 1, "takesValue": true}]}}}
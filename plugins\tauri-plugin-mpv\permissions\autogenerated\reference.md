## Default Permission

Default permissions for the plugin

#### This default permission set includes the following:

- `allow-init`
- `allow-destroy`
- `allow-command`
- `allow-set-video-margin-ratio`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`mpv:allow-command`

</td>
<td>

Enables the command command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:deny-command`

</td>
<td>

Denies the command command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:allow-destroy`

</td>
<td>

Enables the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:deny-destroy`

</td>
<td>

Denies the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:allow-init`

</td>
<td>

Enables the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:deny-init`

</td>
<td>

Denies the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:allow-set-video-margin-ratio`

</td>
<td>

Enables the set_video_margin_ratio command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`mpv:deny-set-video-margin-ratio`

</td>
<td>

Denies the set_video_margin_ratio command without any pre-configured scope.

</td>
</tr>
</table>

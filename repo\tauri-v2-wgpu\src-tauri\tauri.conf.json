{"build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "identifier": "com.tauri.alpha", "bundle": {"active": false, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "app": {"macOSPrivateApi": true, "withGlobalTauri": true, "windows": [{"fullscreen": false, "resizable": true, "title": "alpha-desktop", "width": 800, "height": 600, "decorations": true, "transparent": true}]}}